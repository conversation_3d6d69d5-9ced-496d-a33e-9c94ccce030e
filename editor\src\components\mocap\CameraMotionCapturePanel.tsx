/**
 * 摄像头动作捕捉控制面板
 * 提供摄像头动作捕捉功能的配置和控制界面
 */
import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Form,
  Switch,
  Select,
  Slider,
  Button,
  Space,
  Divider,
  Alert,
  Progress,
  Statistic,
  Row,
  Col,
  Tooltip,
  Modal,
  Steps,
  Typography
} from 'antd';
import {
  CameraOutlined,
  SettingOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Option } = Select;
const { Step } = Steps;
const { Text, Title } = Typography;

/**
 * 摄像头设备信息
 */
interface CameraDevice {
  deviceId: string;
  label: string;
  kind: MediaDeviceKind;
  isDefault: boolean;
}

/**
 * 摄像头动作捕捉配置
 */
interface CameraMotionCaptureConfig {
  enabled: boolean;
  cameraDeviceId: string;
  resolution: { width: number; height: number };
  frameRate: number;
  smoothingFactor: number;
  handTrackingEnabled: boolean;
  gestureRecognitionEnabled: boolean;
  virtualInteractionEnabled: boolean;
  debug: boolean;
}

/**
 * 处理统计信息
 */
interface ProcessingStats {
  totalFrames: number;
  successfulFrames: number;
  averageProcessingTime: number;
  currentFPS: number;
  poseDetectionRate: number;
  handDetectionRate: number;
}

/**
 * 摄像头状态
 */
enum CameraState {
  IDLE = 'idle',
  INITIALIZING = 'initializing',
  ACTIVE = 'active',
  ERROR = 'error',
  STOPPED = 'stopped'
}

/**
 * 摄像头动作捕捉面板属性
 */
interface CameraMotionCapturePanelProps {
  /** 是否显示标题 */
  showTitle?: boolean;
  /** 初始配置 */
  initialConfig?: Partial<CameraMotionCaptureConfig>;
  /** 配置变化回调 */
  onConfigChange?: (config: CameraMotionCaptureConfig) => void;
  /** 启用状态变化回调 */
  onEnabledChange?: (enabled: boolean) => void;
}

/**
 * 摄像头动作捕捉控制面板
 */
const CameraMotionCapturePanel: React.FC<CameraMotionCapturePanelProps> = ({
  showTitle = true,
  initialConfig = {},
  onConfigChange,
  onEnabledChange
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();

  // 状态管理
  const [config, setConfig] = useState<CameraMotionCaptureConfig>({
    enabled: false,
    cameraDeviceId: '',
    resolution: { width: 640, height: 480 },
    frameRate: 30,
    smoothingFactor: 0.5,
    handTrackingEnabled: true,
    gestureRecognitionEnabled: true,
    virtualInteractionEnabled: true,
    debug: false,
    ...initialConfig
  });

  const [availableCameras, setAvailableCameras] = useState<CameraDevice[]>([]);
  const [cameraState, setCameraState] = useState<CameraState>(CameraState.IDLE);
  const [stats, setStats] = useState<ProcessingStats>({
    totalFrames: 0,
    successfulFrames: 0,
    averageProcessingTime: 0,
    currentFPS: 0,
    poseDetectionRate: 0,
    handDetectionRate: 0
  });

  const [isCalibrating, setIsCalibrating] = useState(false);
  const [showCalibrationModal, setShowCalibrationModal] = useState(false);
  const [calibrationStep, setCalibrationStep] = useState(0);
  const [permissionGranted, setPermissionGranted] = useState(false);

  /**
   * 获取可用摄像头设备
   */
  const getAvailableCameras = useCallback(async () => {
    try {
      // 请求摄像头权限
      await navigator.mediaDevices.getUserMedia({ video: true });
      setPermissionGranted(true);

      // 获取设备列表
      const devices = await navigator.mediaDevices.enumerateDevices();
      const cameras = devices
        .filter(device => device.kind === 'videoinput')
        .map(device => ({
          deviceId: device.deviceId,
          label: device.label || `摄像头 ${device.deviceId.slice(0, 8)}`,
          kind: device.kind,
          isDefault: device.deviceId === 'default'
        }));

      setAvailableCameras(cameras);

      // 如果没有选择设备且有可用设备，选择第一个
      if (!config.cameraDeviceId && cameras.length > 0) {
        const newConfig = { ...config, cameraDeviceId: cameras[0].deviceId };
        setConfig(newConfig);
        onConfigChange?.(newConfig);
      }

    } catch (error) {
      console.error('获取摄像头设备失败:', error);
      setPermissionGranted(false);
    }
  }, [config, onConfigChange]);

  /**
   * 处理配置变化
   */
  const handleConfigChange = useCallback((key: keyof CameraMotionCaptureConfig, value: any) => {
    const newConfig = { ...config, [key]: value };
    setConfig(newConfig);
    onConfigChange?.(newConfig);

    // 特殊处理启用状态变化
    if (key === 'enabled') {
      onEnabledChange?.(value);
    }
  }, [config, onConfigChange, onEnabledChange]);

  /**
   * 处理分辨率变化
   */
  const handleResolutionChange = useCallback((resolutionString: string) => {
    const [width, height] = resolutionString.split('x').map(Number);
    handleConfigChange('resolution', { width, height });
  }, [handleConfigChange]);

  /**
   * 开始校准
   */
  const startCalibration = useCallback(() => {
    setShowCalibrationModal(true);
    setCalibrationStep(0);
    setIsCalibrating(true);
  }, []);

  /**
   * 完成校准
   */
  const completeCalibration = useCallback(() => {
    setShowCalibrationModal(false);
    setIsCalibrating(false);
    setCalibrationStep(0);
  }, []);

  /**
   * 重置配置
   */
  const resetConfig = useCallback(() => {
    const defaultConfig: CameraMotionCaptureConfig = {
      enabled: false,
      cameraDeviceId: availableCameras[0]?.deviceId || '',
      resolution: { width: 640, height: 480 },
      frameRate: 30,
      smoothingFactor: 0.5,
      handTrackingEnabled: true,
      gestureRecognitionEnabled: true,
      virtualInteractionEnabled: true,
      debug: false
    };
    
    setConfig(defaultConfig);
    onConfigChange?.(defaultConfig);
    form.setFieldsValue(defaultConfig);
  }, [availableCameras, onConfigChange, form]);

  // 初始化时获取摄像头设备
  useEffect(() => {
    getAvailableCameras();
  }, [getAvailableCameras]);

  // 监听设备变化
  useEffect(() => {
    const handleDeviceChange = () => {
      getAvailableCameras();
    };

    if (navigator.mediaDevices && navigator.mediaDevices.addEventListener) {
      navigator.mediaDevices.addEventListener('devicechange', handleDeviceChange);
      return () => {
        navigator.mediaDevices.removeEventListener('devicechange', handleDeviceChange);
      };
    }
  }, [getAvailableCameras]);

  /**
   * 渲染状态指示器
   */
  const renderStatusIndicator = () => {
    const getStatusColor = () => {
      switch (cameraState) {
        case CameraState.ACTIVE: return 'success';
        case CameraState.INITIALIZING: return 'processing';
        case CameraState.ERROR: return 'exception';
        default: return 'normal';
      }
    };

    const getStatusText = () => {
      switch (cameraState) {
        case CameraState.IDLE: return '未启动';
        case CameraState.INITIALIZING: return '初始化中';
        case CameraState.ACTIVE: return '运行中';
        case CameraState.ERROR: return '错误';
        case CameraState.STOPPED: return '已停止';
        default: return '未知';
      }
    };

    return (
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={8}>
          <Statistic
            title="状态"
            value={getStatusText()}
            prefix={
              cameraState === CameraState.ACTIVE ? 
                <CheckCircleOutlined style={{ color: '#52c41a' }} /> :
                cameraState === CameraState.ERROR ?
                  <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} /> :
                  <InfoCircleOutlined />
            }
          />
        </Col>
        <Col span={8}>
          <Statistic
            title="FPS"
            value={stats.currentFPS}
            precision={1}
            suffix="fps"
          />
        </Col>
        <Col span={8}>
          <Statistic
            title="检测成功率"
            value={stats.poseDetectionRate * 100}
            precision={1}
            suffix="%"
          />
        </Col>
      </Row>
    );
  };

  /**
   * 渲染校准模态框
   */
  const renderCalibrationModal = () => (
    <Modal
      title="摄像头校准向导"
      open={showCalibrationModal}
      onCancel={completeCalibration}
      footer={[
        <Button key="cancel" onClick={completeCalibration}>
          取消
        </Button>,
        <Button
          key="next"
          type="primary"
          onClick={() => {
            if (calibrationStep < 2) {
              setCalibrationStep(calibrationStep + 1);
            } else {
              completeCalibration();
            }
          }}
        >
          {calibrationStep < 2 ? '下一步' : '完成'}
        </Button>
      ]}
      width={600}
    >
      <Steps current={calibrationStep} style={{ marginBottom: 24 }}>
        <Step title="准备" description="检查摄像头和环境" />
        <Step title="姿态校准" description="调整身体姿态" />
        <Step title="完成" description="校准完成" />
      </Steps>

      {calibrationStep === 0 && (
        <div>
          <Alert
            message="校准准备"
            description="请确保摄像头正常工作，光线充足，背景简洁。"
            type="info"
            style={{ marginBottom: 16 }}
          />
          <ul>
            <li>确保摄像头已连接并正常工作</li>
            <li>保持良好的光线条件</li>
            <li>选择简洁的背景</li>
            <li>确保身体完全在摄像头视野内</li>
          </ul>
        </div>
      )}

      {calibrationStep === 1 && (
        <div>
          <Alert
            message="姿态校准"
            description="请按照提示做出相应的姿态动作。"
            type="info"
            style={{ marginBottom: 16 }}
          />
          <div style={{ textAlign: 'center' }}>
            <p>请保持自然站立姿态，双臂自然下垂</p>
            <Progress percent={66} status="active" />
          </div>
        </div>
      )}

      {calibrationStep === 2 && (
        <div>
          <Alert
            message="校准完成"
            description="摄像头动作捕捉已准备就绪！"
            type="success"
            style={{ marginBottom: 16 }}
          />
          <p>校准已完成，您现在可以开始使用摄像头动作捕捉功能。</p>
        </div>
      )}
    </Modal>
  );

  return (
    <Card
      title={showTitle ? (
        <Space>
          <CameraOutlined />
          摄像头动作捕捉
        </Space>
      ) : null}
      size="small"
      extra={
        <Space>
          <Tooltip title="刷新设备">
            <Button
              type="text"
              icon={<ReloadOutlined />}
              onClick={getAvailableCameras}
              size="small"
            />
          </Tooltip>
          <Tooltip title="设置">
            <Button
              type="text"
              icon={<SettingOutlined />}
              size="small"
            />
          </Tooltip>
        </Space>
      }
    >
      {/* 权限检查 */}
      {!permissionGranted && (
        <Alert
          message="需要摄像头权限"
          description="请允许访问摄像头以使用动作捕捉功能。"
          type="warning"
          action={
            <Button size="small" onClick={getAvailableCameras}>
              授权
            </Button>
          }
          style={{ marginBottom: 16 }}
        />
      )}

      {/* 状态指示器 */}
      {renderStatusIndicator()}

      <Form
        form={form}
        layout="vertical"
        initialValues={config}
        size="small"
      >
        {/* 主开关 */}
        <Form.Item label="启用摄像头动作捕捉">
          <Switch
            checked={config.enabled}
            onChange={(enabled) => handleConfigChange('enabled', enabled)}
            checkedChildren="开启"
            unCheckedChildren="关闭"
          />
        </Form.Item>

        {config.enabled && (
          <>
            <Divider />

            {/* 摄像头设备选择 */}
            <Form.Item label="摄像头设备">
              <Select
                value={config.cameraDeviceId}
                onChange={(deviceId) => handleConfigChange('cameraDeviceId', deviceId)}
                placeholder="选择摄像头设备"
                disabled={!permissionGranted}
              >
                {availableCameras.map(camera => (
                  <Option key={camera.deviceId} value={camera.deviceId}>
                    {camera.label}
                    {camera.isDefault && ' (默认)'}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            {/* 分辨率设置 */}
            <Form.Item label="分辨率">
              <Select
                value={`${config.resolution.width}x${config.resolution.height}`}
                onChange={handleResolutionChange}
              >
                <Option value="640x480">640x480 (标清)</Option>
                <Option value="1280x720">1280x720 (高清)</Option>
                <Option value="1920x1080">1920x1080 (全高清)</Option>
              </Select>
            </Form.Item>

            {/* 帧率设置 */}
            <Form.Item label={`帧率: ${config.frameRate} fps`}>
              <Slider
                value={config.frameRate}
                onChange={(frameRate) => handleConfigChange('frameRate', frameRate)}
                min={15}
                max={60}
                step={15}
                marks={{ 15: '15', 30: '30', 60: '60' }}
              />
            </Form.Item>

            {/* 平滑系数 */}
            <Form.Item label={`动作平滑度: ${config.smoothingFactor}`}>
              <Slider
                value={config.smoothingFactor}
                onChange={(smoothingFactor) => handleConfigChange('smoothingFactor', smoothingFactor)}
                min={0}
                max={1}
                step={0.1}
                marks={{ 0: '0', 0.5: '0.5', 1: '1' }}
              />
            </Form.Item>

            <Divider />

            {/* 功能开关 */}
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="手部追踪">
                  <Switch
                    checked={config.handTrackingEnabled}
                    onChange={(enabled) => handleConfigChange('handTrackingEnabled', enabled)}
                    size="small"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="手势识别">
                  <Switch
                    checked={config.gestureRecognitionEnabled}
                    onChange={(enabled) => handleConfigChange('gestureRecognitionEnabled', enabled)}
                    size="small"
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="虚拟交互">
                  <Switch
                    checked={config.virtualInteractionEnabled}
                    onChange={(enabled) => handleConfigChange('virtualInteractionEnabled', enabled)}
                    size="small"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="调试模式">
                  <Switch
                    checked={config.debug}
                    onChange={(enabled) => handleConfigChange('debug', enabled)}
                    size="small"
                  />
                </Form.Item>
              </Col>
            </Row>

            <Divider />

            {/* 操作按钮 */}
            <Space style={{ width: '100%', justifyContent: 'center' }}>
              <Button
                type="primary"
                icon={<PlayCircleOutlined />}
                loading={isCalibrating}
                onClick={startCalibration}
              >
                开始校准
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={resetConfig}
              >
                重置设置
              </Button>
            </Space>
          </>
        )}
      </Form>

      {/* 校准模态框 */}
      {renderCalibrationModal()}
    </Card>
  );
};

export default CameraMotionCapturePanel;
