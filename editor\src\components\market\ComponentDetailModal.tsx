/**
 * ComponentDetailModal.tsx
 * 
 * 组件详情模态框
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Modal,
  Tabs,
  Card,
  Button,
  Space,
  Tag,
  Rate,
  Avatar,
  Divider,
  List,
  Comment,
  Form,
  Input,
  message,
  Image,
  Descriptions,
  Typography,
  Badge,
  Tooltip
} from 'antd';
import {
  DownloadOutlined,
  StarOutlined,
  UserOutlined,
  CalendarOutlined,
  EyeOutlined,
  LikeOutlined,
  DislikeOutlined,
  ShareAltOutlined,
  BugOutlined,
  CodeOutlined
} from '@ant-design/icons';
import { ComponentMarketService } from '../../services/ComponentMarketService';
import type { ComponentData } from './ComponentMarket';
import './ComponentDetailModal.module.css';

const { TabPane } = Tabs;
const { TextArea } = Input;
const { Paragraph, Text, Title } = Typography;

/**
 * 评分数据接口
 */
interface RatingData {
  id: string;
  rating: number;
  comment?: string;
  user: {
    id: string;
    username: string;
    avatar?: string;
  };
  createdAt: string;
  helpfulCount: number;
  isHelpful: boolean;
}

/**
 * 组件详情模态框属性
 */
export interface ComponentDetailModalProps {
  /** 是否显示 */
  visible: boolean;
  /** 组件数据 */
  component: ComponentData | null;
  /** 关闭回调 */
  onClose: () => void;
  /** 下载回调 */
  onDownload?: (component: ComponentData) => void;
  /** 选择回调 */
  onSelect?: (component: ComponentData) => void;
}

/**
 * 组件详情模态框
 */
export const ComponentDetailModal: React.FC<ComponentDetailModalProps> = ({
  visible,
  component,
  onClose,
  onDownload,
  onSelect
}) => {
  const [loading, setLoading] = useState(false);
  const [ratings, setRatings] = useState<RatingData[]>([]);
  const [userRating, setUserRating] = useState<number>(0);
  const [userComment, setUserComment] = useState<string>('');
  const [submittingRating, setSubmittingRating] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  const [form] = Form.useForm();
  const marketService = new ComponentMarketService();

  // 加载组件详情和评分
  const loadComponentDetails = useCallback(async () => {
    if (!component?.id) return;

    setLoading(true);
    try {
      const response = await marketService.getComponentById(component.id);
      if (response.success) {
        setRatings(response.data.ratings || []);
      }
    } catch (error) {
      console.error('加载组件详情失败:', error);
    } finally {
      setLoading(false);
    }
  }, [component?.id, marketService]);

  useEffect(() => {
    if (visible && component) {
      loadComponentDetails();
    }
  }, [visible, component, loadComponentDetails]);

  // 提交评分
  const handleSubmitRating = useCallback(async () => {
    if (!component || userRating === 0) {
      message.warning('请先选择评分');
      return;
    }

    setSubmittingRating(true);
    try {
      const response = await marketService.rateComponent(
        component.id,
        userRating,
        userComment
      );

      if (response.success) {
        message.success('评分提交成功');
        setUserRating(0);
        setUserComment('');
        form.resetFields();
        loadComponentDetails();
      } else {
        message.error(response.message || '评分提交失败');
      }
    } catch (error) {
      message.error('评分提交失败，请稍后重试');
    } finally {
      setSubmittingRating(false);
    }
  }, [component, userRating, userComment, form, marketService, loadComponentDetails]);

  // 处理下载
  const handleDownload = useCallback(() => {
    if (component && onDownload) {
      onDownload(component);
    }
  }, [component, onDownload]);

  // 处理选择
  const handleSelect = useCallback(() => {
    if (component && onSelect) {
      onSelect(component);
    }
  }, [component, onSelect]);

  // 处理分享
  const handleShare = useCallback(() => {
    if (component) {
      const url = `${window.location.origin}/market/components/${component.id}`;
      navigator.clipboard.writeText(url).then(() => {
        message.success('链接已复制到剪贴板');
      });
    }
  }, [component]);

  // 渲染概览标签页
  const renderOverviewTab = () => (
    <div className="overview-content">
      {/* 组件信息 */}
      <Card title="组件信息" size="small">
        <Descriptions column={2} size="small">
          <Descriptions.Item label="名称">{component?.name}</Descriptions.Item>
          <Descriptions.Item label="版本">{component?.version}</Descriptions.Item>
          <Descriptions.Item label="分类">{component?.category}</Descriptions.Item>
          <Descriptions.Item label="作者">
            <Space>
              <Avatar
                size="small"
                src={component?.author.avatar}
                icon={<UserOutlined />}
              />
              {component?.author.username}
            </Space>
          </Descriptions.Item>
          <Descriptions.Item label="下载量">
            <Badge count={component?.downloads} color="blue" />
          </Descriptions.Item>
          <Descriptions.Item label="评分">
            <Space>
              <Rate disabled allowHalf value={component?.rating} />
              <span>({component?.ratingCount} 评分)</span>
            </Space>
          </Descriptions.Item>
          <Descriptions.Item label="创建时间">
            {component?.createdAt ? new Date(component.createdAt).toLocaleDateString() : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="更新时间">
            {component?.updatedAt ? new Date(component.updatedAt).toLocaleDateString() : '-'}
          </Descriptions.Item>
        </Descriptions>
      </Card>

      {/* 组件描述 */}
      <Card title="组件描述" size="small" style={{ marginTop: 16 }}>
        <Paragraph>{component?.description}</Paragraph>
      </Card>

      {/* 标签 */}
      <Card title="标签" size="small" style={{ marginTop: 16 }}>
        <Space wrap>
          {component?.tags.map(tag => (
            <Tag key={tag} color="blue">{tag}</Tag>
          ))}
        </Space>
      </Card>

      {/* 预览图片 */}
      {component?.previewImage && (
        <Card title="预览图片" size="small" style={{ marginTop: 16 }}>
          <Image
            src={component.previewImage}
            alt={component.name}
            style={{ maxWidth: '100%' }}
          />
        </Card>
      )}
    </div>
  );

  // 渲染评分标签页
  const renderRatingsTab = () => (
    <div className="ratings-content">
      {/* 评分统计 */}
      <Card size="small" className="rating-summary">
        <div className="rating-overview">
          <div className="rating-score">
            <Title level={2}>{component?.rating?.toFixed(1) || '0.0'}</Title>
            <Rate disabled allowHalf value={component?.rating} />
            <Text type="secondary">基于 {component?.ratingCount} 个评分</Text>
          </div>
        </div>
      </Card>

      {/* 添加评分 */}
      <Card title="添加评分" size="small" style={{ marginTop: 16 }}>
        <Form form={form} layout="vertical">
          <Form.Item label="评分">
            <Rate
              value={userRating}
              onChange={setUserRating}
              allowClear
            />
          </Form.Item>
          <Form.Item label="评论（可选）">
            <TextArea
              value={userComment}
              onChange={(e) => setUserComment(e.target.value)}
              placeholder="分享您的使用体验..."
              rows={3}
            />
          </Form.Item>
          <Form.Item>
            <Button
              type="primary"
              onClick={handleSubmitRating}
              loading={submittingRating}
              disabled={userRating === 0}
            >
              提交评分
            </Button>
          </Form.Item>
        </Form>
      </Card>

      {/* 评分列表 */}
      <Card title="用户评分" size="small" style={{ marginTop: 16 }}>
        <List
          dataSource={ratings}
          renderItem={(rating) => (
            <Comment
              author={rating.user.username}
              avatar={
                <Avatar
                  src={rating.user.avatar}
                  icon={<UserOutlined />}
                />
              }
              content={
                <div>
                  <Rate disabled value={rating.rating} style={{ fontSize: 14 }} />
                  {rating.comment && <Paragraph style={{ marginTop: 8 }}>{rating.comment}</Paragraph>}
                </div>
              }
              datetime={
                <Tooltip title={new Date(rating.createdAt).toLocaleString()}>
                  <span>{new Date(rating.createdAt).toLocaleDateString()}</span>
                </Tooltip>
              }
              actions={[
                <Tooltip title="有帮助">
                  <Button
                    type="text"
                    size="small"
                    icon={<LikeOutlined />}
                  >
                    {rating.helpfulCount}
                  </Button>
                </Tooltip>
              ]}
            />
          )}
          locale={{ emptyText: '暂无评分' }}
        />
      </Card>
    </div>
  );

  // 渲染文档标签页
  const renderDocumentationTab = () => (
    <div className="documentation-content">
      <Card title="使用文档" size="small">
        {component?.documentation ? (
          <div
            className="markdown-content"
            dangerouslySetInnerHTML={{ __html: component.documentation }}
          />
        ) : (
          <Text type="secondary">暂无文档</Text>
        )}
      </Card>
    </div>
  );

  if (!component) return null;

  return (
    <Modal
      title={
        <div className="modal-title">
          <Space>
            <span>{component.name}</span>
            <Tag color="blue">v{component.version}</Tag>
            {component.isFeatured && <Badge count="精选" color="gold" />}
            {component.isPopular && <Badge count="热门" color="red" />}
          </Space>
        </div>
      }
      open={visible}
      onCancel={onClose}
      width={800}
      footer={
        <Space>
          <Button onClick={onClose}>关闭</Button>
          <Button icon={<ShareAltOutlined />} onClick={handleShare}>
            分享
          </Button>
          {onSelect && (
            <Button
              type="default"
              icon={<StarOutlined />}
              onClick={handleSelect}
            >
              选择组件
            </Button>
          )}
          {onDownload && (
            <Button
              type="primary"
              icon={<DownloadOutlined />}
              onClick={handleDownload}
            >
              下载组件
            </Button>
          )}
        </Space>
      }
      className="component-detail-modal"
    >
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane
          tab={
            <span>
              <EyeOutlined />
              概览
            </span>
          }
          key="overview"
        >
          {renderOverviewTab()}
        </TabPane>
        
        <TabPane
          tab={
            <span>
              <StarOutlined />
              评分 ({component.ratingCount})
            </span>
          }
          key="ratings"
        >
          {renderRatingsTab()}
        </TabPane>
        
        <TabPane
          tab={
            <span>
              <CodeOutlined />
              文档
            </span>
          }
          key="documentation"
        >
          {renderDocumentationTab()}
        </TabPane>
      </Tabs>
    </Modal>
  );
};

export default ComponentDetailModal;
