/**
 * 行为树可视化编辑器
 * 
 * 提供直观的行为树编辑界面，支持拖拽创建、连接节点、属性配置等功能。
 * 与底层行为树引擎深度集成，实现所见即所得的编辑体验。
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { 
  Card, 
  Button, 
  Space, 
  Drawer, 
  Tree, 
  Form, 
  Input, 
  Select, 
  InputNumber,
  Switch,
  Tooltip,
  message,
  Modal,
  Tabs
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  SaveOutlined,
  LoadingOutlined,
  BugOutlined,
  SettingOutlined,
  PlusOutlined,
  DeleteOutlined,
  CopyOutlined
} from '@ant-design/icons';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import * as THREE from 'three';
import './BehaviorTreeEditor.css';

// 导入行为树引擎
import {
  BehaviorTreeEngine,
  BehaviorNodeType,
  BehaviorNodeStatus,
  BehaviorNode,
  SequenceNode,
  SelectorNode,
  ParallelNode,
  ActionNode,
  ConditionNode,
  WaitNode,
  InverterNode,
  RepeaterNode,
  Blackboard
} from '../../libs/dl-engine.mjs';

const { TabPane } = Tabs;
const { Option } = Select;

/**
 * 节点配置接口
 */
interface NodeConfig {
  id: string;
  name: string;
  type: BehaviorNodeType;
  position: { x: number; y: number };
  properties: { [key: string]: any };
  children: string[];
  parent?: string;
}

/**
 * 行为树配置
 */
interface BehaviorTreeConfig {
  id: string;
  name: string;
  description: string;
  nodes: { [key: string]: NodeConfig };
  rootNodeId: string;
  blackboardData: { [key: string]: any };
}

/**
 * 节点类型定义
 */
const NODE_TYPES = {
  [BehaviorNodeType.SEQUENCE]: {
    name: '顺序节点',
    description: '按顺序执行子节点，全部成功才成功',
    color: '#52c41a',
    icon: '→',
    category: 'composite'
  },
  [BehaviorNodeType.SELECTOR]: {
    name: '选择节点',
    description: '选择第一个成功的子节点',
    color: '#1890ff',
    icon: '?',
    category: 'composite'
  },
  [BehaviorNodeType.PARALLEL]: {
    name: '并行节点',
    description: '同时执行所有子节点',
    color: '#722ed1',
    icon: '||',
    category: 'composite'
  },
  [BehaviorNodeType.INVERTER]: {
    name: '反转节点',
    description: '反转子节点的执行结果',
    color: '#fa8c16',
    icon: '!',
    category: 'decorator'
  },
  [BehaviorNodeType.REPEATER]: {
    name: '重复节点',
    description: '重复执行子节点',
    color: '#eb2f96',
    icon: '↻',
    category: 'decorator'
  },
  [BehaviorNodeType.ACTION]: {
    name: '动作节点',
    description: '执行具体的动作',
    color: '#f5222d',
    icon: '▶',
    category: 'leaf'
  },
  [BehaviorNodeType.CONDITION]: {
    name: '条件节点',
    description: '检查特定条件',
    color: '#faad14',
    icon: '?',
    category: 'leaf'
  },
  [BehaviorNodeType.WAIT]: {
    name: '等待节点',
    description: '等待指定时间',
    color: '#13c2c2',
    icon: '⏱',
    category: 'leaf'
  }
};

/**
 * 可拖拽的节点组件
 */
const DraggableNode: React.FC<{
  type: BehaviorNodeType;
  onDrop: (type: BehaviorNodeType, position: { x: number; y: number }) => void;
}> = ({ type, onDrop }) => {
  const [{ isDragging }, drag] = useDrag({
    type: 'node',
    item: { type },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const nodeType = NODE_TYPES[type];

  return (
    <div
      ref={drag}
      className={`node-palette-item ${isDragging ? 'dragging' : ''}`}
      style={{ 
        borderColor: nodeType.color,
        opacity: isDragging ? 0.5 : 1 
      }}
    >
      <div className="node-icon" style={{ color: nodeType.color }}>
        {nodeType.icon}
      </div>
      <div className="node-info">
        <div className="node-name">{nodeType.name}</div>
        <div className="node-description">{nodeType.description}</div>
      </div>
    </div>
  );
};

/**
 * 行为树节点组件
 */
const BehaviorTreeNodeComponent: React.FC<{
  config: NodeConfig;
  isSelected: boolean;
  isExecuting: boolean;
  status: BehaviorNodeStatus;
  onSelect: (id: string) => void;
  onMove: (id: string, position: { x: number; y: number }) => void;
  onDelete: (id: string) => void;
}> = ({ config, isSelected, isExecuting, status, onSelect, onMove, onDelete }) => {
  const nodeType = NODE_TYPES[config.type];
  
  const getStatusColor = () => {
    switch (status) {
      case BehaviorNodeStatus.SUCCESS:
        return '#52c41a';
      case BehaviorNodeStatus.FAILURE:
        return '#f5222d';
      case BehaviorNodeStatus.RUNNING:
        return '#1890ff';
      default:
        return '#d9d9d9';
    }
  };

  return (
    <div
      className={`behavior-node ${isSelected ? 'selected' : ''} ${isExecuting ? 'executing' : ''}`}
      style={{
        left: config.position.x,
        top: config.position.y,
        borderColor: isExecuting ? getStatusColor() : nodeType.color,
        backgroundColor: isSelected ? '#e6f7ff' : '#fff'
      }}
      onClick={() => onSelect(config.id)}
    >
      <div className="node-header" style={{ backgroundColor: nodeType.color }}>
        <span className="node-icon">{nodeType.icon}</span>
        <span className="node-title">{config.name}</span>
        <Button
          type="text"
          size="small"
          icon={<DeleteOutlined />}
          onClick={(e) => {
            e.stopPropagation();
            onDelete(config.id);
          }}
          className="node-delete-btn"
        />
      </div>
      <div className="node-body">
        <div className="node-type">{nodeType.name}</div>
        {isExecuting && (
          <div className="node-status" style={{ color: getStatusColor() }}>
            {status}
          </div>
        )}
      </div>
    </div>
  );
};

/**
 * 行为树编辑器主组件
 */
const BehaviorTreeEditor: React.FC<{
  entityId?: string;
  onSave?: (config: BehaviorTreeConfig) => void;
  onLoad?: () => BehaviorTreeConfig | null;
}> = ({ entityId, onSave, onLoad }) => {
  // 状态管理
  const [treeConfig, setTreeConfig] = useState<BehaviorTreeConfig>({
    id: 'behavior_tree_' + Date.now(),
    name: '新行为树',
    description: '',
    nodes: {},
    rootNodeId: '',
    blackboardData: {}
  });
  
  const [selectedNodeId, setSelectedNodeId] = useState<string>('');
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionStatus, setExecutionStatus] = useState<{ [key: string]: BehaviorNodeStatus }>({});
  const [showNodePalette, setShowNodePalette] = useState(true);
  const [showProperties, setShowProperties] = useState(true);
  const [showBlackboard, setShowBlackboard] = useState(false);
  
  // 引擎实例
  const [engine] = useState(() => new BehaviorTreeEngine());
  const [blackboard] = useState(() => new Blackboard());
  
  const canvasRef = useRef<HTMLDivElement>(null);

  /**
   * 画布拖放处理
   */
  const [, drop] = useDrop({
    accept: 'node',
    drop: (item: { type: BehaviorNodeType }, monitor) => {
      const offset = monitor.getClientOffset();
      const canvasRect = canvasRef.current?.getBoundingClientRect();
      
      if (offset && canvasRect) {
        const position = {
          x: offset.x - canvasRect.left,
          y: offset.y - canvasRect.top
        };
        handleAddNode(item.type, position);
      }
    },
  });

  /**
   * 添加节点
   */
  const handleAddNode = useCallback((type: BehaviorNodeType, position: { x: number; y: number }) => {
    const nodeId = `node_${Date.now()}`;
    const newNode: NodeConfig = {
      id: nodeId,
      name: NODE_TYPES[type].name,
      type,
      position,
      properties: {},
      children: []
    };

    setTreeConfig(prev => ({
      ...prev,
      nodes: {
        ...prev.nodes,
        [nodeId]: newNode
      },
      rootNodeId: prev.rootNodeId || nodeId
    }));

    setSelectedNodeId(nodeId);
    message.success(`已添加${NODE_TYPES[type].name}`);
  }, []);

  /**
   * 删除节点
   */
  const handleDeleteNode = useCallback((nodeId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个节点吗？',
      onOk: () => {
        setTreeConfig(prev => {
          const newNodes = { ...prev.nodes };
          delete newNodes[nodeId];
          
          // 移除父子关系
          Object.values(newNodes).forEach(node => {
            node.children = node.children.filter(id => id !== nodeId);
          });

          return {
            ...prev,
            nodes: newNodes,
            rootNodeId: prev.rootNodeId === nodeId ? '' : prev.rootNodeId
          };
        });
        
        if (selectedNodeId === nodeId) {
          setSelectedNodeId('');
        }
        
        message.success('节点已删除');
      }
    });
  }, [selectedNodeId]);

  /**
   * 更新节点属性
   */
  const handleUpdateNodeProperties = useCallback((nodeId: string, properties: any) => {
    setTreeConfig(prev => ({
      ...prev,
      nodes: {
        ...prev.nodes,
        [nodeId]: {
          ...prev.nodes[nodeId],
          ...properties
        }
      }
    }));
  }, []);

  /**
   * 执行行为树
   */
  const handleExecute = useCallback(() => {
    if (!treeConfig.rootNodeId) {
      message.error('请先设置根节点');
      return;
    }

    try {
      // 构建行为树
      const rootNode = buildBehaviorTree(treeConfig);
      engine.createTree(treeConfig.id, rootNode);
      
      setIsExecuting(true);
      
      // 模拟执行
      const executeStep = () => {
        if (!isExecuting) return;
        
        const status = engine.executeTree(treeConfig.id, 0.016); // 60fps
        
        // 更新执行状态（这里需要从引擎获取详细状态）
        setExecutionStatus(prev => ({
          ...prev,
          [treeConfig.rootNodeId]: status || BehaviorNodeStatus.RUNNING
        }));
        
        if (status === BehaviorNodeStatus.SUCCESS || status === BehaviorNodeStatus.FAILURE) {
          setIsExecuting(false);
          message.success(`执行完成: ${status}`);
        } else {
          setTimeout(executeStep, 100);
        }
      };
      
      executeStep();
      
    } catch (error) {
      console.error('执行失败:', error);
      message.error('执行失败: ' + (error as Error).message);
    }
  }, [treeConfig, engine, isExecuting]);

  /**
   * 停止执行
   */
  const handleStop = useCallback(() => {
    setIsExecuting(false);
    setExecutionStatus({});
    engine.resetTree(treeConfig.id);
    message.info('已停止执行');
  }, [treeConfig.id, engine]);

  /**
   * 保存行为树
   */
  const handleSave = useCallback(() => {
    if (onSave) {
      onSave(treeConfig);
      message.success('行为树已保存');
    }
  }, [treeConfig, onSave]);

  /**
   * 构建行为树实例
   */
  const buildBehaviorTree = (config: BehaviorTreeConfig): BehaviorNode => {
    const nodeInstances: { [key: string]: BehaviorNode } = {};
    
    // 创建所有节点实例
    Object.values(config.nodes).forEach(nodeConfig => {
      let node: BehaviorNode;
      
      switch (nodeConfig.type) {
        case BehaviorNodeType.SEQUENCE:
          node = new SequenceNode(nodeConfig.id, nodeConfig.name, blackboard);
          break;
        case BehaviorNodeType.SELECTOR:
          node = new SelectorNode(nodeConfig.id, nodeConfig.name, blackboard);
          break;
        case BehaviorNodeType.PARALLEL:
          node = new ParallelNode(nodeConfig.id, nodeConfig.name, blackboard);
          break;
        case BehaviorNodeType.INVERTER:
          node = new InverterNode(nodeConfig.id, nodeConfig.name, blackboard);
          break;
        case BehaviorNodeType.REPEATER:
          node = new RepeaterNode(nodeConfig.id, nodeConfig.name, blackboard);
          break;
        case BehaviorNodeType.WAIT:
          node = new WaitNode(nodeConfig.id, nodeConfig.name, blackboard, nodeConfig.properties.waitTime || 1000);
          break;
        default:
          // 对于动作和条件节点，需要具体实现
          throw new Error(`不支持的节点类型: ${nodeConfig.type}`);
      }
      
      nodeInstances[nodeConfig.id] = node;
    });
    
    // 建立父子关系
    Object.values(config.nodes).forEach(nodeConfig => {
      const node = nodeInstances[nodeConfig.id];
      nodeConfig.children.forEach(childId => {
        const childNode = nodeInstances[childId];
        if (childNode) {
          node.addChild(childNode);
        }
      });
    });
    
    return nodeInstances[config.rootNodeId];
  };

  /**
   * 渲染工具栏
   */
  const renderToolbar = () => (
    <div className="behavior-tree-toolbar">
      <Space>
        <Button
          type="primary"
          icon={isExecuting ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
          onClick={isExecuting ? handleStop : handleExecute}
          disabled={!treeConfig.rootNodeId}
        >
          {isExecuting ? '停止' : '执行'}
        </Button>
        
        <Button
          icon={<StopOutlined />}
          onClick={handleStop}
          disabled={!isExecuting}
        >
          重置
        </Button>
        
        <Button
          icon={<SaveOutlined />}
          onClick={handleSave}
        >
          保存
        </Button>
        
        <Button
          icon={<BugOutlined />}
          onClick={() => setShowBlackboard(!showBlackboard)}
        >
          调试
        </Button>
        
        <Button
          icon={<SettingOutlined />}
          onClick={() => setShowProperties(!showProperties)}
        >
          属性
        </Button>
      </Space>
    </div>
  );

  /**
   * 渲染节点面板
   */
  const renderNodePalette = () => (
    <Card title="节点库" size="small" className="node-palette">
      <Tabs defaultActiveKey="composite">
        <TabPane tab="复合节点" key="composite">
          {Object.entries(NODE_TYPES)
            .filter(([, type]) => type.category === 'composite')
            .map(([nodeType]) => (
              <DraggableNode
                key={nodeType}
                type={nodeType as BehaviorNodeType}
                onDrop={handleAddNode}
              />
            ))}
        </TabPane>
        <TabPane tab="装饰节点" key="decorator">
          {Object.entries(NODE_TYPES)
            .filter(([, type]) => type.category === 'decorator')
            .map(([nodeType]) => (
              <DraggableNode
                key={nodeType}
                type={nodeType as BehaviorNodeType}
                onDrop={handleAddNode}
              />
            ))}
        </TabPane>
        <TabPane tab="叶子节点" key="leaf">
          {Object.entries(NODE_TYPES)
            .filter(([, type]) => type.category === 'leaf')
            .map(([nodeType]) => (
              <DraggableNode
                key={nodeType}
                type={nodeType as BehaviorNodeType}
                onDrop={handleAddNode}
              />
            ))}
        </TabPane>
      </Tabs>
    </Card>
  );

  /**
   * 渲染属性面板
   */
  const renderPropertiesPanel = () => {
    const selectedNode = selectedNodeId ? treeConfig.nodes[selectedNodeId] : null;
    
    if (!selectedNode) {
      return (
        <Card title="属性" size="small" className="properties-panel">
          <div className="no-selection">请选择一个节点</div>
        </Card>
      );
    }

    return (
      <Card title="属性" size="small" className="properties-panel">
        <Form layout="vertical" size="small">
          <Form.Item label="节点名称">
            <Input
              value={selectedNode.name}
              onChange={(e) => handleUpdateNodeProperties(selectedNodeId, { name: e.target.value })}
            />
          </Form.Item>
          
          <Form.Item label="节点类型">
            <Input value={NODE_TYPES[selectedNode.type].name} disabled />
          </Form.Item>
          
          {selectedNode.type === BehaviorNodeType.WAIT && (
            <Form.Item label="等待时间(ms)">
              <InputNumber
                value={selectedNode.properties.waitTime || 1000}
                onChange={(value) => handleUpdateNodeProperties(selectedNodeId, { 
                  properties: { ...selectedNode.properties, waitTime: value }
                })}
              />
            </Form.Item>
          )}
          
          {selectedNode.type === BehaviorNodeType.PARALLEL && (
            <>
              <Form.Item label="成功阈值">
                <InputNumber
                  value={selectedNode.properties.successThreshold || 1}
                  min={1}
                  onChange={(value) => handleUpdateNodeProperties(selectedNodeId, { 
                    properties: { ...selectedNode.properties, successThreshold: value }
                  })}
                />
              </Form.Item>
              <Form.Item label="失败阈值">
                <InputNumber
                  value={selectedNode.properties.failureThreshold || 1}
                  min={1}
                  onChange={(value) => handleUpdateNodeProperties(selectedNodeId, { 
                    properties: { ...selectedNode.properties, failureThreshold: value }
                  })}
                />
              </Form.Item>
            </>
          )}
        </Form>
      </Card>
    );
  };

  /**
   * 渲染画布
   */
  const renderCanvas = () => (
    <div 
      ref={(node) => {
        canvasRef.current = node;
        drop(node);
      }}
      className="behavior-tree-canvas"
    >
      {Object.values(treeConfig.nodes).map(node => (
        <BehaviorTreeNodeComponent
          key={node.id}
          config={node}
          isSelected={selectedNodeId === node.id}
          isExecuting={isExecuting}
          status={executionStatus[node.id] || BehaviorNodeStatus.INVALID}
          onSelect={setSelectedNodeId}
          onMove={(id, position) => handleUpdateNodeProperties(id, { position })}
          onDelete={handleDeleteNode}
        />
      ))}
      
      {/* 连接线渲染 */}
      <svg className="connection-lines">
        {Object.values(treeConfig.nodes).map(node =>
          node.children.map(childId => {
            const childNode = treeConfig.nodes[childId];
            if (!childNode) return null;
            
            return (
              <line
                key={`${node.id}-${childId}`}
                x1={node.position.x + 100}
                y1={node.position.y + 50}
                x2={childNode.position.x + 100}
                y2={childNode.position.y}
                stroke="#d9d9d9"
                strokeWidth="2"
              />
            );
          })
        )}
      </svg>
    </div>
  );

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="behavior-tree-editor">
        {renderToolbar()}
        
        <div className="editor-layout">
          {showNodePalette && (
            <div className="left-panel">
              {renderNodePalette()}
            </div>
          )}
          
          <div className="center-panel">
            {renderCanvas()}
          </div>
          
          {showProperties && (
            <div className="right-panel">
              {renderPropertiesPanel()}
            </div>
          )}
        </div>
        
        {/* 黑板调试抽屉 */}
        <Drawer
          title="黑板数据"
          placement="bottom"
          height={300}
          open={showBlackboard}
          onClose={() => setShowBlackboard(false)}
        >
          <div className="blackboard-debug">
            <pre>{JSON.stringify(treeConfig.blackboardData, null, 2)}</pre>
          </div>
        </Drawer>
      </div>
    </DndProvider>
  );
};

export default BehaviorTreeEditor;
