/**
 * PluginManager.tsx
 * 
 * 插件管理器界面
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Layout,
  Card,
  Button,
  Switch,
  Space,
  Tag,
  Avatar,
  List,
  Modal,
  Form,
  Input,
  Upload,
  message,
  Tabs,
  Descriptions,
  Alert,
  Popconfirm,
  Badge,
  Tooltip,
  Empty
} from 'antd';
import {
  AppstoreOutlined,
  SettingOutlined,
  DownloadOutlined,
  DeleteOutlined,
  InfoCircleOutlined,
  UploadOutlined,
  ReloadOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  PlusOutlined
} from '@ant-design/icons';
import { pluginSystem, Plugin } from '../../core/PluginSystem';
import './PluginManager.module.css';

const { Content, Sider } = Layout;
const { TabPane } = Tabs;
const { TextArea } = Input;

/**
 * 插件状态枚举
 */
enum PluginStatus {
  ENABLED = 'enabled',
  DISABLED = 'disabled',
  ERROR = 'error'
}

/**
 * 插件管理器属性
 */
export interface PluginManagerProps {
  /** 是否显示 */
  visible: boolean;
  /** 关闭回调 */
  onClose: () => void;
  /** 样式类名 */
  className?: string;
}

/**
 * 插件管理器组件
 */
export const PluginManager: React.FC<PluginManagerProps> = ({
  visible,
  onClose,
  className
}) => {
  const [plugins, setPlugins] = useState<Plugin[]>([]);
  const [selectedPlugin, setSelectedPlugin] = useState<Plugin | null>(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('installed');
  const [isInstallModalVisible, setIsInstallModalVisible] = useState(false);
  const [installForm] = Form.useForm();

  // 加载插件列表
  const loadPlugins = useCallback(() => {
    const allPlugins = pluginSystem.getPlugins();
    setPlugins(allPlugins);
    
    // 如果当前选中的插件不存在，清空选择
    if (selectedPlugin && !allPlugins.find(p => p.id === selectedPlugin.id)) {
      setSelectedPlugin(null);
    }
  }, [selectedPlugin]);

  // 初始化
  useEffect(() => {
    if (visible) {
      loadPlugins();
    }
  }, [visible, loadPlugins]);

  // 监听插件系统事件
  useEffect(() => {
    const handlePluginChange = () => {
      loadPlugins();
    };

    pluginSystem.on('pluginInstalled', handlePluginChange);
    pluginSystem.on('pluginUninstalled', handlePluginChange);
    pluginSystem.on('pluginEnabled', handlePluginChange);
    pluginSystem.on('pluginDisabled', handlePluginChange);

    return () => {
      pluginSystem.off('pluginInstalled', handlePluginChange);
      pluginSystem.off('pluginUninstalled', handlePluginChange);
      pluginSystem.off('pluginEnabled', handlePluginChange);
      pluginSystem.off('pluginDisabled', handlePluginChange);
    };
  }, [loadPlugins]);

  // 切换插件启用状态
  const handleTogglePlugin = useCallback(async (plugin: Plugin) => {
    setLoading(true);
    try {
      if (plugin.enabled) {
        await pluginSystem.disablePlugin(plugin.id);
        message.success(`插件 ${plugin.name} 已禁用`);
      } else {
        await pluginSystem.enablePlugin(plugin.id);
        message.success(`插件 ${plugin.name} 已启用`);
      }
    } catch (error) {
      message.error(error.message);
    } finally {
      setLoading(false);
    }
  }, []);

  // 卸载插件
  const handleUninstallPlugin = useCallback(async (plugin: Plugin) => {
    setLoading(true);
    try {
      await pluginSystem.uninstallPlugin(plugin.id);
      message.success(`插件 ${plugin.name} 已卸载`);
      if (selectedPlugin?.id === plugin.id) {
        setSelectedPlugin(null);
      }
    } catch (error) {
      message.error(error.message);
    } finally {
      setLoading(false);
    }
  }, [selectedPlugin]);

  // 安装插件
  const handleInstallPlugin = useCallback(async () => {
    try {
      const values = await installForm.validateFields();
      setLoading(true);

      // 这里应该实现插件安装逻辑
      // 可以从文件、URL或插件市场安装
      
      message.success('插件安装成功');
      setIsInstallModalVisible(false);
      installForm.resetFields();
    } catch (error) {
      if (error.errorFields) {
        // 表单验证错误
        return;
      }
      message.error('插件安装失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  }, [installForm]);

  // 获取插件状态
  const getPluginStatus = useCallback((plugin: Plugin): PluginStatus => {
    if (plugin.enabled) {
      return PluginStatus.ENABLED;
    }
    return PluginStatus.DISABLED;
  }, []);

  // 获取状态图标
  const getStatusIcon = useCallback((status: PluginStatus) => {
    switch (status) {
      case PluginStatus.ENABLED:
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case PluginStatus.DISABLED:
        return <CloseCircleOutlined style={{ color: '#d9d9d9' }} />;
      case PluginStatus.ERROR:
        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return null;
    }
  }, []);

  // 渲染插件列表
  const renderPluginList = useCallback(() => {
    const filteredPlugins = plugins.filter(plugin => {
      switch (activeTab) {
        case 'enabled':
          return plugin.enabled;
        case 'disabled':
          return !plugin.enabled;
        default:
          return true;
      }
    });

    return (
      <List
        dataSource={filteredPlugins}
        renderItem={(plugin) => {
          const status = getPluginStatus(plugin);
          return (
            <List.Item
              className={`plugin-item ${selectedPlugin?.id === plugin.id ? 'selected' : ''}`}
              onClick={() => setSelectedPlugin(plugin)}
              actions={[
                <Switch
                  checked={plugin.enabled}
                  loading={loading}
                  onChange={() => handleTogglePlugin(plugin)}
                  onClick={(e) => e.stopPropagation()}
                />,
                <Popconfirm
                  title="确定要卸载此插件吗？"
                  onConfirm={() => handleUninstallPlugin(plugin)}
                  okText="确定"
                  cancelText="取消"
                >
                  <Button
                    type="text"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={(e) => e.stopPropagation()}
                  />
                </Popconfirm>
              ]}
            >
              <List.Item.Meta
                avatar={
                  <Badge dot={status === PluginStatus.ERROR} color="red">
                    <Avatar icon={<AppstoreOutlined />} />
                  </Badge>
                }
                title={
                  <Space>
                    <span>{plugin.name}</span>
                    <Tag size="small">v{plugin.version}</Tag>
                    {getStatusIcon(status)}
                  </Space>
                }
                description={
                  <div>
                    <p>{plugin.description}</p>
                    <Space size="small">
                      <span>作者: {plugin.author}</span>
                      {plugin.dependencies && plugin.dependencies.length > 0 && (
                        <Tooltip title={`依赖: ${plugin.dependencies.join(', ')}`}>
                          <Tag size="small" color="blue">
                            依赖 {plugin.dependencies.length}
                          </Tag>
                        </Tooltip>
                      )}
                    </Space>
                  </div>
                }
              />
            </List.Item>
          );
        }}
        locale={{ emptyText: '暂无插件' }}
      />
    );
  }, [plugins, activeTab, selectedPlugin, loading, getPluginStatus, getStatusIcon, handleTogglePlugin, handleUninstallPlugin]);

  // 渲染插件详情
  const renderPluginDetail = useCallback(() => {
    if (!selectedPlugin) {
      return (
        <Empty
          description="请选择一个插件查看详情"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      );
    }

    const status = getPluginStatus(selectedPlugin);

    return (
      <div className="plugin-detail">
        <Card
          title={
            <Space>
              <span>{selectedPlugin.name}</span>
              <Tag color={status === PluginStatus.ENABLED ? 'green' : 'default'}>
                {status === PluginStatus.ENABLED ? '已启用' : '已禁用'}
              </Tag>
            </Space>
          }
          extra={
            <Space>
              <Button
                type={selectedPlugin.enabled ? 'default' : 'primary'}
                loading={loading}
                onClick={() => handleTogglePlugin(selectedPlugin)}
              >
                {selectedPlugin.enabled ? '禁用' : '启用'}
              </Button>
              <Popconfirm
                title="确定要卸载此插件吗？"
                onConfirm={() => handleUninstallPlugin(selectedPlugin)}
                okText="确定"
                cancelText="取消"
              >
                <Button danger icon={<DeleteOutlined />}>
                  卸载
                </Button>
              </Popconfirm>
            </Space>
          }
        >
          <Descriptions column={2} size="small">
            <Descriptions.Item label="版本">{selectedPlugin.version}</Descriptions.Item>
            <Descriptions.Item label="作者">{selectedPlugin.author}</Descriptions.Item>
            <Descriptions.Item label="入口文件">{selectedPlugin.main}</Descriptions.Item>
            <Descriptions.Item label="状态">
              <Space>
                {getStatusIcon(status)}
                <span>{status === PluginStatus.ENABLED ? '已启用' : '已禁用'}</span>
              </Space>
            </Descriptions.Item>
          </Descriptions>

          <div style={{ marginTop: 16 }}>
            <h4>描述</h4>
            <p>{selectedPlugin.description}</p>
          </div>

          {selectedPlugin.dependencies && selectedPlugin.dependencies.length > 0 && (
            <div style={{ marginTop: 16 }}>
              <h4>依赖项</h4>
              <Space wrap>
                {selectedPlugin.dependencies.map(dep => (
                  <Tag key={dep} color="blue">{dep}</Tag>
                ))}
              </Space>
            </div>
          )}

          {selectedPlugin.config && Object.keys(selectedPlugin.config).length > 0 && (
            <div style={{ marginTop: 16 }}>
              <h4>配置</h4>
              <pre className="config-display">
                {JSON.stringify(selectedPlugin.config, null, 2)}
              </pre>
            </div>
          )}
        </Card>
      </div>
    );
  }, [selectedPlugin, loading, getPluginStatus, getStatusIcon, handleTogglePlugin, handleUninstallPlugin]);

  return (
    <Modal
      title="插件管理器"
      open={visible}
      onCancel={onClose}
      width={1000}
      footer={
        <Space>
          <Button onClick={onClose}>关闭</Button>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setIsInstallModalVisible(true)}
          >
            安装插件
          </Button>
          <Button icon={<ReloadOutlined />} onClick={loadPlugins}>
            刷新
          </Button>
        </Space>
      }
      className={`plugin-manager ${className || ''}`}
    >
      <Layout style={{ height: 600 }}>
        {/* 插件列表 */}
        <Sider width={400} className="plugin-list-sider">
          <div className="sider-content">
            <Tabs activeKey={activeTab} onChange={setActiveTab} size="small">
              <TabPane tab={`全部 (${plugins.length})`} key="installed" />
              <TabPane 
                tab={`已启用 (${plugins.filter(p => p.enabled).length})`} 
                key="enabled" 
              />
              <TabPane 
                tab={`已禁用 (${plugins.filter(p => !p.enabled).length})`} 
                key="disabled" 
              />
            </Tabs>
            
            <div className="plugin-list">
              {renderPluginList()}
            </div>
          </div>
        </Sider>

        {/* 插件详情 */}
        <Content className="plugin-detail-content">
          {renderPluginDetail()}
        </Content>
      </Layout>

      {/* 安装插件模态框 */}
      <Modal
        title="安装插件"
        open={isInstallModalVisible}
        onOk={handleInstallPlugin}
        onCancel={() => {
          setIsInstallModalVisible(false);
          installForm.resetFields();
        }}
        confirmLoading={loading}
        okText="安装"
        cancelText="取消"
      >
        <Alert
          message="插件安装说明"
          description="您可以通过上传插件文件、输入插件URL或从插件市场安装插件。请确保插件来源可信。"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Form form={installForm} layout="vertical">
          <Form.Item
            name="installMethod"
            label="安装方式"
            rules={[{ required: true, message: '请选择安装方式' }]}
            initialValue="file"
          >
            <Tabs>
              <TabPane tab="上传文件" key="file">
                <Form.Item
                  name="pluginFile"
                  rules={[{ required: true, message: '请选择插件文件' }]}
                >
                  <Upload
                    accept=".zip,.js,.json"
                    beforeUpload={() => false}
                    maxCount={1}
                  >
                    <Button icon={<UploadOutlined />}>选择插件文件</Button>
                  </Upload>
                </Form.Item>
              </TabPane>
              
              <TabPane tab="URL安装" key="url">
                <Form.Item
                  name="pluginUrl"
                  rules={[
                    { required: true, message: '请输入插件URL' },
                    { type: 'url', message: '请输入有效的URL' }
                  ]}
                >
                  <Input placeholder="https://example.com/plugin.js" />
                </Form.Item>
              </TabPane>
              
              <TabPane tab="插件市场" key="market">
                <Form.Item
                  name="pluginId"
                  rules={[{ required: true, message: '请输入插件ID' }]}
                >
                  <Input placeholder="输入插件ID" />
                </Form.Item>
              </TabPane>
            </Tabs>
          </Form.Item>
        </Form>
      </Modal>
    </Modal>
  );
};

export default PluginManager;
