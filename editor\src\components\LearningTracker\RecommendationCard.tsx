import React, { useState } from 'react';
import { Card, Button, Tag, Progress, Rate, Modal, message, Tooltip } from 'antd';
import { 
  ClockCircleOutlined, 
  BookOutlined, 
  PlayCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  EyeInvisibleOutlined,
  StarOutlined
} from '@ant-design/icons';
import { LearningAnalyticsService } from '../../services/LearningAnalyticsService';
import { useLearningTracker } from './LearningDataProvider';

/**
 * 推荐卡片组件属性
 */
interface RecommendationCardProps {
  recommendation: {
    id: string;
    title: string;
    description: string;
    type: string;
    difficulty: string;
    estimatedDuration: number;
    reason: string;
    tags: string[];
    knowledgeArea: string;
    relevanceScore: number;
    status?: string;
  };
  onFeedback?: (recommendationId: string, action: string, rating?: number) => void;
  showActions?: boolean;
}

/**
 * 推荐卡片组件
 * 展示个性化推荐内容并支持用户反馈
 */
export const RecommendationCard: React.FC<RecommendationCardProps> = ({
  recommendation,
  onFeedback,
  showActions = true
}) => {
  const [loading, setLoading] = useState(false);
  const [ratingModalVisible, setRatingModalVisible] = useState(false);
  const [rating, setRating] = useState(0);
  const [comment, setComment] = useState('');
  const { trackRecommendationReceived } = useLearningTracker();
  
  const analyticsService = new LearningAnalyticsService();

  /**
   * 获取类型图标
   */
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'video':
        return <PlayCircleOutlined />;
      case 'article':
        return <BookOutlined />;
      case 'exercise':
        return <CheckCircleOutlined />;
      default:
        return <BookOutlined />;
    }
  };

  /**
   * 获取类型标签颜色
   */
  const getTypeColor = (type: string) => {
    switch (type) {
      case 'video':
        return 'red';
      case 'article':
        return 'blue';
      case 'exercise':
        return 'green';
      case 'interactive':
        return 'purple';
      default:
        return 'default';
    }
  };

  /**
   * 获取难度标签颜色
   */
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return 'green';
      case 'medium':
        return 'orange';
      case 'hard':
        return 'red';
      default:
        return 'default';
    }
  };

  /**
   * 获取类型中文名称
   */
  const getTypeName = (type: string) => {
    switch (type) {
      case 'video':
        return '视频';
      case 'article':
        return '文章';
      case 'exercise':
        return '练习';
      case 'interactive':
        return '互动';
      case 'concept':
        return '概念';
      default:
        return type;
    }
  };

  /**
   * 获取难度中文名称
   */
  const getDifficultyName = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return '简单';
      case 'medium':
        return '中等';
      case 'hard':
        return '困难';
      default:
        return difficulty;
    }
  };

  /**
   * 处理推荐反馈
   */
  const handleFeedback = async (action: 'accepted' | 'rejected' | 'ignored') => {
    try {
      setLoading(true);

      // 记录用户行为
      await trackRecommendationReceived({
        recommendationId: recommendation.id,
        contentTitle: recommendation.title,
        contentType: recommendation.type,
        relevanceScore: recommendation.relevanceScore,
        userAction: action,
        knowledgeArea: recommendation.knowledgeArea
      });

      // 发送反馈到服务器
      await analyticsService.updateRecommendationFeedback(recommendation.id, {
        action
      });

      // 调用回调函数
      if (onFeedback) {
        onFeedback(recommendation.id, action);
      }

      // 显示成功消息
      const actionText = action === 'accepted' ? '接受' : action === 'rejected' ? '拒绝' : '忽略';
      message.success(`已${actionText}推荐`);

      // 如果是接受，显示评分对话框
      if (action === 'accepted') {
        setRatingModalVisible(true);
      }

    } catch (error: any) {
      message.error(error.message || '操作失败');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 提交评分
   */
  const handleRatingSubmit = async () => {
    try {
      setLoading(true);

      await analyticsService.updateRecommendationFeedback(recommendation.id, {
        action: 'completed',
        rating,
        comment
      });

      if (onFeedback) {
        onFeedback(recommendation.id, 'completed', rating);
      }

      message.success('评分提交成功');
      setRatingModalVisible(false);
      setRating(0);
      setComment('');

    } catch (error: any) {
      message.error(error.message || '评分提交失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Card
        size="small"
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            {getTypeIcon(recommendation.type)}
            <span>{recommendation.title}</span>
          </div>
        }
        extra={
          <div style={{ display: 'flex', gap: 4 }}>
            <Tag color={getTypeColor(recommendation.type)}>
              {getTypeName(recommendation.type)}
            </Tag>
            <Tag color={getDifficultyColor(recommendation.difficulty)}>
              {getDifficultyName(recommendation.difficulty)}
            </Tag>
          </div>
        }
        style={{ marginBottom: 16 }}
        actions={showActions ? [
          <Tooltip title="接受推荐">
            <Button 
              type="text" 
              icon={<CheckCircleOutlined />} 
              onClick={() => handleFeedback('accepted')}
              loading={loading}
              style={{ color: '#52c41a' }}
            >
              接受
            </Button>
          </Tooltip>,
          <Tooltip title="拒绝推荐">
            <Button 
              type="text" 
              icon={<CloseCircleOutlined />} 
              onClick={() => handleFeedback('rejected')}
              loading={loading}
              style={{ color: '#ff4d4f' }}
            >
              拒绝
            </Button>
          </Tooltip>,
          <Tooltip title="忽略推荐">
            <Button 
              type="text" 
              icon={<EyeInvisibleOutlined />} 
              onClick={() => handleFeedback('ignored')}
              loading={loading}
              style={{ color: '#8c8c8c' }}
            >
              忽略
            </Button>
          </Tooltip>
        ] : undefined}
      >
        <div style={{ marginBottom: 12 }}>
          <p style={{ fontSize: 13, color: '#666', marginBottom: 8, lineHeight: 1.4 }}>
            {recommendation.description}
          </p>
        </div>

        <div style={{ marginBottom: 12 }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: 16, fontSize: 12, color: '#666' }}>
            <span>
              <ClockCircleOutlined style={{ marginRight: 4 }} />
              {recommendation.estimatedDuration}分钟
            </span>
            <span>
              <BookOutlined style={{ marginRight: 4 }} />
              {recommendation.knowledgeArea}
            </span>
          </div>
        </div>

        <div style={{ marginBottom: 12 }}>
          <div style={{ fontSize: 12, color: '#1890ff', marginBottom: 4 }}>
            推荐理由
          </div>
          <p style={{ fontSize: 12, color: '#666', margin: 0, lineHeight: 1.3 }}>
            {recommendation.reason}
          </p>
        </div>

        <div style={{ marginBottom: 12 }}>
          <div style={{ fontSize: 12, color: '#666', marginBottom: 4 }}>
            相关度
          </div>
          <Progress 
            percent={Math.round(recommendation.relevanceScore * 100)}
            size="small"
            strokeColor={{
              '0%': '#108ee9',
              '100%': '#87d068',
            }}
            format={() => `${Math.round(recommendation.relevanceScore * 100)}%`}
          />
        </div>

        {recommendation.tags && recommendation.tags.length > 0 && (
          <div>
            <div style={{ fontSize: 12, color: '#666', marginBottom: 4 }}>
              标签
            </div>
            <div>
              {recommendation.tags.map((tag, index) => (
                <Tag key={index} size="small" style={{ marginBottom: 2 }}>
                  {tag}
                </Tag>
              ))}
            </div>
          </div>
        )}
      </Card>

      {/* 评分对话框 */}
      <Modal
        title="为这个推荐评分"
        open={ratingModalVisible}
        onOk={handleRatingSubmit}
        onCancel={() => {
          setRatingModalVisible(false);
          setRating(0);
          setComment('');
        }}
        confirmLoading={loading}
        okText="提交"
        cancelText="取消"
      >
        <div style={{ textAlign: 'center', marginBottom: 16 }}>
          <h4>{recommendation.title}</h4>
          <p style={{ color: '#666' }}>请为这个推荐的质量评分</p>
        </div>

        <div style={{ textAlign: 'center', marginBottom: 16 }}>
          <Rate
            value={rating}
            onChange={setRating}
            character={<StarOutlined />}
            style={{ fontSize: 24 }}
          />
          <div style={{ marginTop: 8, fontSize: 12, color: '#666' }}>
            {rating === 0 && '请选择评分'}
            {rating === 1 && '很差'}
            {rating === 2 && '较差'}
            {rating === 3 && '一般'}
            {rating === 4 && '较好'}
            {rating === 5 && '很好'}
          </div>
        </div>

        <div>
          <p style={{ marginBottom: 8 }}>评论 (可选):</p>
          <textarea
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            placeholder="请分享您对这个推荐的看法..."
            style={{
              width: '100%',
              minHeight: 60,
              padding: 8,
              border: '1px solid #d9d9d9',
              borderRadius: 4,
              resize: 'vertical'
            }}
          />
        </div>
      </Modal>
    </>
  );
};
