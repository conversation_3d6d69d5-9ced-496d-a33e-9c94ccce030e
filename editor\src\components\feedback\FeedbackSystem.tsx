/**
 * 用户反馈收集系统
 * 收集用户对UI组件的使用体验和改进建议
 */
import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Rate,
  Select,
  Button,
  Space,
  Card,
  List,
  Avatar,
  Tag,
  Tooltip,
  message,
  Drawer,
  Badge,
  FloatButton,
  Divider,
  Progress,
  Typography
} from 'antd';
import {
  MessageOutlined,
  StarOutlined,
  BugOutlined,
  BulbOutlined,
  HeartOutlined,
  SendOutlined,
  CloseOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import './FeedbackSystem.less';

const { TextArea } = Input;
const { Option } = Select;
const { Text, Title } = Typography;

// 反馈类型
export enum FeedbackType {
  BUG = 'bug',
  FEATURE = 'feature',
  IMPROVEMENT = 'improvement',
  PRAISE = 'praise',
  QUESTION = 'question'
}

// 反馈优先级
export enum FeedbackPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent'
}

// 反馈状态
export enum FeedbackStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  RESOLVED = 'resolved',
  CLOSED = 'closed'
}

// 反馈数据接口
export interface FeedbackData {
  id?: string;
  type: FeedbackType;
  title: string;
  description: string;
  rating: number;
  priority: FeedbackPriority;
  status: FeedbackStatus;
  component?: string;
  browserInfo?: string;
  userAgent?: string;
  screenshot?: string;
  userId?: string;
  userEmail?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

// 反馈统计接口
export interface FeedbackStats {
  total: number;
  byType: Record<FeedbackType, number>;
  byPriority: Record<FeedbackPriority, number>;
  byStatus: Record<FeedbackStatus, number>;
  averageRating: number;
  responseTime: number;
}

// 反馈系统属性
interface FeedbackSystemProps {
  visible?: boolean;
  onClose?: () => void;
  currentComponent?: string;
  showFloatButton?: boolean;
}

// 反馈类型配置
const FEEDBACK_TYPE_CONFIG = {
  [FeedbackType.BUG]: {
    label: '错误报告',
    icon: <BugOutlined />,
    color: '#ff4d4f',
    description: '报告功能异常或错误'
  },
  [FeedbackType.FEATURE]: {
    label: '功能建议',
    icon: <BulbOutlined />,
    color: '#1890ff',
    description: '建议新功能或改进'
  },
  [FeedbackType.IMPROVEMENT]: {
    label: '体验优化',
    icon: <StarOutlined />,
    color: '#faad14',
    description: '改善用户体验'
  },
  [FeedbackType.PRAISE]: {
    label: '表扬建议',
    icon: <HeartOutlined />,
    color: '#52c41a',
    description: '分享使用心得'
  },
  [FeedbackType.QUESTION]: {
    label: '使用问题',
    icon: <ExclamationCircleOutlined />,
    color: '#722ed1',
    description: '使用过程中的疑问'
  }
};

// 优先级配置
const PRIORITY_CONFIG = {
  [FeedbackPriority.LOW]: { label: '低', color: '#52c41a' },
  [FeedbackPriority.MEDIUM]: { label: '中', color: '#faad14' },
  [FeedbackPriority.HIGH]: { label: '高', color: '#ff7a45' },
  [FeedbackPriority.URGENT]: { label: '紧急', color: '#ff4d4f' }
};

export const FeedbackSystem: React.FC<FeedbackSystemProps> = ({
  visible = false,
  onClose,
  currentComponent,
  showFloatButton = true
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  
  // 状态管理
  const [isModalVisible, setIsModalVisible] = useState(visible);
  const [isDrawerVisible, setIsDrawerVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [feedbackList, setFeedbackList] = useState<FeedbackData[]>([]);
  const [feedbackStats, setFeedbackStats] = useState<FeedbackStats | null>(null);
  const [selectedType, setSelectedType] = useState<FeedbackType>(FeedbackType.IMPROVEMENT);

  // 监听外部visible变化
  useEffect(() => {
    setIsModalVisible(visible);
  }, [visible]);

  // 获取浏览器信息
  const getBrowserInfo = () => {
    const userAgent = navigator.userAgent;
    const browserInfo = {
      userAgent,
      language: navigator.language,
      platform: navigator.platform,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      screenResolution: `${screen.width}x${screen.height}`,
      viewportSize: `${window.innerWidth}x${window.innerHeight}`,
      timestamp: new Date().toISOString()
    };
    return JSON.stringify(browserInfo, null, 2);
  };

  // 提交反馈
  const handleSubmitFeedback = async (values: any) => {
    setLoading(true);
    
    try {
      const feedbackData: FeedbackData = {
        ...values,
        type: selectedType,
        component: currentComponent,
        browserInfo: getBrowserInfo(),
        userAgent: navigator.userAgent,
        userId: 'current-user-id', // 从用户上下文获取
        createdAt: new Date(),
        status: FeedbackStatus.PENDING
      };

      // 这里应该调用API提交反馈
      console.log('提交反馈:', feedbackData);
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      message.success('反馈提交成功，感谢您的建议！');
      
      // 重置表单
      form.resetFields();
      setIsModalVisible(false);
      onClose?.();
      
      // 刷新反馈列表
      await loadFeedbackList();
      
    } catch (error) {
      message.error('反馈提交失败，请稍后重试');
      console.error('提交反馈失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 加载反馈列表
  const loadFeedbackList = async () => {
    try {
      // 这里应该调用API获取反馈列表
      // const response = await feedbackAPI.getFeedbackList();
      
      // 模拟数据
      const mockFeedbackList: FeedbackData[] = [
        {
          id: '1',
          type: FeedbackType.BUG,
          title: '拖拽功能在Chrome中异常',
          description: '在Chrome浏览器中拖拽UI元素时会出现卡顿现象',
          rating: 3,
          priority: FeedbackPriority.HIGH,
          status: FeedbackStatus.IN_PROGRESS,
          component: 'UIDragDropSystem',
          createdAt: new Date('2024-01-15')
        },
        {
          id: '2',
          type: FeedbackType.FEATURE,
          title: '希望增加快捷键支持',
          description: '建议为常用操作添加键盘快捷键，提高操作效率',
          rating: 5,
          priority: FeedbackPriority.MEDIUM,
          status: FeedbackStatus.PENDING,
          component: 'UIVisualEditor',
          createdAt: new Date('2024-01-14')
        },
        {
          id: '3',
          type: FeedbackType.PRAISE,
          title: '新的主题系统很棒',
          description: '新的主题切换功能非常流畅，界面也很美观',
          rating: 5,
          priority: FeedbackPriority.LOW,
          status: FeedbackStatus.RESOLVED,
          component: 'UIThemeSystem',
          createdAt: new Date('2024-01-13')
        }
      ];
      
      setFeedbackList(mockFeedbackList);
      
      // 计算统计信息
      const stats: FeedbackStats = {
        total: mockFeedbackList.length,
        byType: {
          [FeedbackType.BUG]: mockFeedbackList.filter(f => f.type === FeedbackType.BUG).length,
          [FeedbackType.FEATURE]: mockFeedbackList.filter(f => f.type === FeedbackType.FEATURE).length,
          [FeedbackType.IMPROVEMENT]: mockFeedbackList.filter(f => f.type === FeedbackType.IMPROVEMENT).length,
          [FeedbackType.PRAISE]: mockFeedbackList.filter(f => f.type === FeedbackType.PRAISE).length,
          [FeedbackType.QUESTION]: mockFeedbackList.filter(f => f.type === FeedbackType.QUESTION).length
        },
        byPriority: {
          [FeedbackPriority.LOW]: mockFeedbackList.filter(f => f.priority === FeedbackPriority.LOW).length,
          [FeedbackPriority.MEDIUM]: mockFeedbackList.filter(f => f.priority === FeedbackPriority.MEDIUM).length,
          [FeedbackPriority.HIGH]: mockFeedbackList.filter(f => f.priority === FeedbackPriority.HIGH).length,
          [FeedbackPriority.URGENT]: mockFeedbackList.filter(f => f.priority === FeedbackPriority.URGENT).length
        },
        byStatus: {
          [FeedbackStatus.PENDING]: mockFeedbackList.filter(f => f.status === FeedbackStatus.PENDING).length,
          [FeedbackStatus.IN_PROGRESS]: mockFeedbackList.filter(f => f.status === FeedbackStatus.IN_PROGRESS).length,
          [FeedbackStatus.RESOLVED]: mockFeedbackList.filter(f => f.status === FeedbackStatus.RESOLVED).length,
          [FeedbackStatus.CLOSED]: mockFeedbackList.filter(f => f.status === FeedbackStatus.CLOSED).length
        },
        averageRating: mockFeedbackList.reduce((sum, f) => sum + f.rating, 0) / mockFeedbackList.length,
        responseTime: 24 // 小时
      };
      
      setFeedbackStats(stats);
      
    } catch (error) {
      message.error('加载反馈列表失败');
      console.error('加载反馈列表失败:', error);
    }
  };

  // 初始化加载数据
  useEffect(() => {
    loadFeedbackList();
  }, []);

  // 渲染反馈类型选择
  const renderTypeSelection = () => (
    <div className="feedback-type-selection">
      <Title level={5}>选择反馈类型</Title>
      <div className="type-cards">
        {Object.entries(FEEDBACK_TYPE_CONFIG).map(([type, config]) => (
          <Card
            key={type}
            size="small"
            className={`type-card ${selectedType === type ? 'selected' : ''}`}
            onClick={() => setSelectedType(type as FeedbackType)}
            hoverable
          >
            <div className="type-icon" style={{ color: config.color }}>
              {config.icon}
            </div>
            <div className="type-label">{config.label}</div>
            <div className="type-description">{config.description}</div>
          </Card>
        ))}
      </div>
    </div>
  );

  // 渲染反馈表单
  const renderFeedbackForm = () => (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSubmitFeedback}
      initialValues={{
        priority: FeedbackPriority.MEDIUM,
        rating: 4
      }}
    >
      <Form.Item
        name="title"
        label="反馈标题"
        rules={[{ required: true, message: '请输入反馈标题' }]}
      >
        <Input placeholder="简要描述您的反馈" />
      </Form.Item>

      <Form.Item
        name="description"
        label="详细描述"
        rules={[{ required: true, message: '请输入详细描述' }]}
      >
        <TextArea
          rows={4}
          placeholder="请详细描述您遇到的问题或建议"
        />
      </Form.Item>

      <Form.Item
        name="priority"
        label="优先级"
      >
        <Select>
          {Object.entries(PRIORITY_CONFIG).map(([priority, config]) => (
            <Option key={priority} value={priority}>
              <Tag color={config.color}>{config.label}</Tag>
            </Option>
          ))}
        </Select>
      </Form.Item>

      <Form.Item
        name="rating"
        label="满意度评分"
      >
        <Rate allowHalf />
      </Form.Item>

      {currentComponent && (
        <Form.Item label="相关组件">
          <Input value={currentComponent} disabled />
        </Form.Item>
      )}
    </Form>
  );

  // 渲染反馈统计
  const renderFeedbackStats = () => {
    if (!feedbackStats) return null;

    return (
      <div className="feedback-stats">
        <Title level={5}>反馈统计</Title>
        <div className="stats-cards">
          <Card size="small">
            <div className="stat-item">
              <div className="stat-value">{feedbackStats.total}</div>
              <div className="stat-label">总反馈数</div>
            </div>
          </Card>
          <Card size="small">
            <div className="stat-item">
              <div className="stat-value">{feedbackStats.averageRating.toFixed(1)}</div>
              <div className="stat-label">平均评分</div>
            </div>
          </Card>
          <Card size="small">
            <div className="stat-item">
              <div className="stat-value">{feedbackStats.responseTime}h</div>
              <div className="stat-label">响应时间</div>
            </div>
          </Card>
        </div>

        <Divider />

        <div className="stats-breakdown">
          <div className="breakdown-section">
            <Text strong>按类型分布</Text>
            {Object.entries(feedbackStats.byType).map(([type, count]) => (
              <div key={type} className="breakdown-item">
                <span>{FEEDBACK_TYPE_CONFIG[type as FeedbackType].label}</span>
                <Progress
                  percent={(count / feedbackStats.total) * 100}
                  size="small"
                  showInfo={false}
                />
                <span>{count}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  // 渲染反馈列表
  const renderFeedbackList = () => (
    <List
      dataSource={feedbackList}
      renderItem={(item) => (
        <List.Item>
          <List.Item.Meta
            avatar={
              <Avatar
                icon={FEEDBACK_TYPE_CONFIG[item.type].icon}
                style={{ backgroundColor: FEEDBACK_TYPE_CONFIG[item.type].color }}
              />
            }
            title={
              <div className="feedback-title">
                <span>{item.title}</span>
                <Space>
                  <Tag color={PRIORITY_CONFIG[item.priority].color}>
                    {PRIORITY_CONFIG[item.priority].label}
                  </Tag>
                  <Rate disabled defaultValue={item.rating} size="small" />
                </Space>
              </div>
            }
            description={
              <div>
                <Text type="secondary">{item.description}</Text>
                <div className="feedback-meta">
                  <Text type="secondary" size="small">
                    {item.component && `组件: ${item.component} • `}
                    {item.createdAt?.toLocaleDateString()}
                  </Text>
                </div>
              </div>
            }
          />
        </List.Item>
      )}
    />
  );

  return (
    <>
      {/* 浮动反馈按钮 */}
      {showFloatButton && (
        <FloatButton
          icon={<MessageOutlined />}
          type="primary"
          tooltip="反馈建议"
          onClick={() => setIsModalVisible(true)}
        />
      )}

      {/* 反馈提交模态框 */}
      <Modal
        title="提交反馈"
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          onClose?.();
        }}
        footer={[
          <Button key="cancel" onClick={() => setIsModalVisible(false)}>
            取消
          </Button>,
          <Button key="view" onClick={() => setIsDrawerVisible(true)}>
            查看反馈
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={loading}
            onClick={() => form.submit()}
            icon={<SendOutlined />}
          >
            提交反馈
          </Button>
        ]}
        width={600}
        className="feedback-modal"
      >
        {renderTypeSelection()}
        <Divider />
        {renderFeedbackForm()}
      </Modal>

      {/* 反馈管理抽屉 */}
      <Drawer
        title="反馈管理"
        placement="right"
        width={800}
        open={isDrawerVisible}
        onClose={() => setIsDrawerVisible(false)}
        extra={
          <Button
            type="primary"
            onClick={() => {
              setIsDrawerVisible(false);
              setIsModalVisible(true);
            }}
          >
            提交新反馈
          </Button>
        }
      >
        {renderFeedbackStats()}
        <Divider />
        <Title level={5}>反馈列表</Title>
        {renderFeedbackList()}
      </Drawer>
    </>
  );
};

export default FeedbackSystem;
