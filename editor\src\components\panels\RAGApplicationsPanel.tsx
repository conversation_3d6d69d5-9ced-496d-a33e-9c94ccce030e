/**
 * RAG应用管理面板
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Table,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  Tag,
  Tooltip,
  Dropdown,
  message,
  Popconfirm,
  Typography,
  Row,
  Col,
  Statistic,
  Progress,
  Steps,
  Divider,
} from 'antd';
import {
  PlusOutlined,
  PlayCircleOutlined,
  StopOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  SettingOutlined,
  RobotOutlined,
  DatabaseOutlined,
  MessageOutlined,
  MoreOutlined,
  ExportOutlined,
  CopyOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import type { ColumnsType } from 'antd/es/table';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;
const { Step } = Steps;

/**
 * RAG应用接口
 */
interface RAGApplication {
  id: string;
  name: string;
  description: string;
  sceneId: string;
  sceneName: string;
  knowledgeBaseId: string;
  knowledgeBaseName: string;
  avatarId: string;
  avatarName: string;
  status: 'draft' | 'active' | 'inactive' | 'error';
  createdAt: string;
  updatedAt: string;
  lastUsed?: string;
  totalSessions: number;
  totalMessages: number;
  averageRating: number;
  config: {
    enableVoiceInteraction: boolean;
    enableTextInteraction: boolean;
    autoStart: boolean;
    maxSessionDuration: number;
    welcomeMessage: string;
  };
}

/**
 * 创建应用步骤
 */
interface CreateStepData {
  basic: {
    name: string;
    description: string;
    sceneId: string;
  };
  knowledgeBase: {
    knowledgeBaseId: string;
  };
  avatar: {
    avatarId: string;
  };
  config: {
    enableVoiceInteraction: boolean;
    enableTextInteraction: boolean;
    autoStart: boolean;
    maxSessionDuration: number;
    welcomeMessage: string;
  };
}

/**
 * RAG应用管理面板组件
 */
const RAGApplicationsPanel: React.FC = () => {
  const { t } = useTranslation();
  const [applications, setApplications] = useState<RAGApplication[]>([]);
  const [loading, setLoading] = useState(false);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [createData, setCreateData] = useState<Partial<CreateStepData>>({});
  const [form] = Form.useForm();

  /**
   * 加载RAG应用列表
   */
  const loadApplications = async () => {
    setLoading(true);
    try {
      // 这里调用API获取RAG应用列表
      const mockData: RAGApplication[] = [
        {
          id: '1',
          name: '医疗咨询助手',
          description: '基于医疗知识库的智能咨询数字人',
          sceneId: 'scene_1',
          sceneName: '医疗展厅',
          knowledgeBaseId: 'kb_1',
          knowledgeBaseName: '医疗知识库',
          avatarId: 'avatar_1',
          avatarName: '小雅医生',
          status: 'active',
          createdAt: '2024-01-15T10:30:00Z',
          updatedAt: '2024-01-20T15:45:00Z',
          lastUsed: '2024-01-22T09:15:00Z',
          totalSessions: 156,
          totalMessages: 1248,
          averageRating: 4.6,
          config: {
            enableVoiceInteraction: true,
            enableTextInteraction: true,
            autoStart: true,
            maxSessionDuration: 1800,
            welcomeMessage: '您好，我是小雅医生，很高兴为您提供医疗咨询服务。',
          },
        },
        {
          id: '2',
          name: '企业培训助手',
          description: '企业培训知识问答数字人',
          sceneId: 'scene_2',
          sceneName: '培训中心',
          knowledgeBaseId: 'kb_2',
          knowledgeBaseName: '培训知识库',
          avatarId: 'avatar_2',
          avatarName: '培训师小李',
          status: 'draft',
          createdAt: '2024-01-18T14:20:00Z',
          updatedAt: '2024-01-21T11:30:00Z',
          totalSessions: 0,
          totalMessages: 0,
          averageRating: 0,
          config: {
            enableVoiceInteraction: true,
            enableTextInteraction: true,
            autoStart: false,
            maxSessionDuration: 3600,
            welcomeMessage: '欢迎来到企业培训中心，我是您的培训助手。',
          },
        },
      ];
      setApplications(mockData);
    } catch (error) {
      message.error('加载RAG应用列表失败');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 创建RAG应用
   */
  const handleCreateApplication = async () => {
    try {
      // 这里调用API创建RAG应用
      const applicationData = {
        ...createData.basic,
        ...createData.knowledgeBase,
        ...createData.avatar,
        config: createData.config,
      };
      console.log('创建RAG应用:', applicationData);
      message.success('RAG应用创建成功');
      setCreateModalVisible(false);
      setCurrentStep(0);
      setCreateData({});
      form.resetFields();
      loadApplications();
    } catch (error) {
      message.error('创建RAG应用失败');
    }
  };

  /**
   * 启动/停止应用
   */
  const handleToggleApplication = async (id: string, action: 'start' | 'stop') => {
    try {
      // 这里调用API启动/停止应用
      console.log(`${action} RAG应用:`, id);
      message.success(`应用${action === 'start' ? '启动' : '停止'}成功`);
      loadApplications();
    } catch (error) {
      message.error(`应用${action === 'start' ? '启动' : '停止'}失败`);
    }
  };

  /**
   * 删除应用
   */
  const handleDeleteApplication = async (id: string) => {
    try {
      // 这里调用API删除应用
      console.log('删除RAG应用:', id);
      message.success('应用删除成功');
      loadApplications();
    } catch (error) {
      message.error('删除应用失败');
    }
  };

  /**
   * 下一步
   */
  const handleNext = async () => {
    try {
      const values = await form.validateFields();
      const stepKey = ['basic', 'knowledgeBase', 'avatar', 'config'][currentStep];
      setCreateData(prev => ({ ...prev, [stepKey]: values }));
      
      if (currentStep < 3) {
        setCurrentStep(currentStep + 1);
        form.resetFields();
      } else {
        await handleCreateApplication();
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  /**
   * 上一步
   */
  const handlePrev = () => {
    setCurrentStep(currentStep - 1);
  };

  /**
   * 应用表格列定义
   */
  const columns: ColumnsType<RAGApplication> = [
    {
      title: '应用名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Space>
          <RobotOutlined />
          <span>{text}</span>
          <Tag color={record.status === 'active' ? 'green' : record.status === 'draft' ? 'blue' : 'red'}>
            {record.status === 'active' ? '运行中' : record.status === 'draft' ? '草稿' : '已停止'}
          </Tag>
        </Space>
      ),
    },
    {
      title: '场景',
      dataIndex: 'sceneName',
      key: 'sceneName',
    },
    {
      title: '知识库',
      dataIndex: 'knowledgeBaseName',
      key: 'knowledgeBaseName',
      render: (text) => (
        <Space>
          <DatabaseOutlined />
          <span>{text}</span>
        </Space>
      ),
    },
    {
      title: '数字人',
      dataIndex: 'avatarName',
      key: 'avatarName',
    },
    {
      title: '会话数',
      dataIndex: 'totalSessions',
      key: 'totalSessions',
      width: 80,
    },
    {
      title: '消息数',
      dataIndex: 'totalMessages',
      key: 'totalMessages',
      width: 80,
    },
    {
      title: '评分',
      dataIndex: 'averageRating',
      key: 'averageRating',
      width: 80,
      render: (rating) => rating > 0 ? rating.toFixed(1) : '-',
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          {record.status === 'active' ? (
            <Tooltip title="停止">
              <Button
                type="text"
                icon={<StopOutlined />}
                onClick={() => handleToggleApplication(record.id, 'stop')}
              />
            </Tooltip>
          ) : (
            <Tooltip title="启动">
              <Button
                type="text"
                icon={<PlayCircleOutlined />}
                onClick={() => handleToggleApplication(record.id, 'start')}
              />
            </Tooltip>
          )}
          <Tooltip title="编辑">
            <Button type="text" icon={<EditOutlined />} />
          </Tooltip>
          <Tooltip title="查看">
            <Button type="text" icon={<EyeOutlined />} />
          </Tooltip>
          <Dropdown
            menu={{
              items: [
                {
                  key: 'config',
                  icon: <SettingOutlined />,
                  label: '配置',
                },
                {
                  key: 'export',
                  icon: <ExportOutlined />,
                  label: '导出',
                },
                {
                  key: 'copy',
                  icon: <CopyOutlined />,
                  label: '复制',
                },
                {
                  key: 'delete',
                  icon: <DeleteOutlined />,
                  label: '删除',
                  danger: true,
                },
              ],
              onClick: ({ key }) => {
                if (key === 'delete') {
                  Modal.confirm({
                    title: '确定删除这个应用吗？',
                    content: '删除后无法恢复',
                    onOk: () => handleDeleteApplication(record.id),
                  });
                }
              },
            }}
          >
            <Button type="text" icon={<MoreOutlined />} />
          </Dropdown>
        </Space>
      ),
    },
  ];

  /**
   * 渲染创建步骤内容
   */
  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <Form form={form} layout="vertical">
            <Form.Item
              name="name"
              label="应用名称"
              rules={[{ required: true, message: '请输入应用名称' }]}
            >
              <Input placeholder="请输入应用名称" />
            </Form.Item>
            <Form.Item
              name="description"
              label="应用描述"
              rules={[{ required: true, message: '请输入应用描述' }]}
            >
              <TextArea rows={3} placeholder="请输入应用描述" />
            </Form.Item>
            <Form.Item
              name="sceneId"
              label="关联场景"
              rules={[{ required: true, message: '请选择关联场景' }]}
            >
              <Select placeholder="请选择关联场景">
                <Option value="scene_1">医疗展厅</Option>
                <Option value="scene_2">培训中心</Option>
                <Option value="scene_3">展示大厅</Option>
              </Select>
            </Form.Item>
          </Form>
        );
      case 1:
        return (
          <Form form={form} layout="vertical">
            <Form.Item
              name="knowledgeBaseId"
              label="选择知识库"
              rules={[{ required: true, message: '请选择知识库' }]}
            >
              <Select placeholder="请选择知识库">
                <Option value="kb_1">医疗知识库</Option>
                <Option value="kb_2">培训知识库</Option>
                <Option value="kb_3">产品知识库</Option>
              </Select>
            </Form.Item>
          </Form>
        );
      case 2:
        return (
          <Form form={form} layout="vertical">
            <Form.Item
              name="avatarId"
              label="选择数字人"
              rules={[{ required: true, message: '请选择数字人' }]}
            >
              <Select placeholder="请选择数字人">
                <Option value="avatar_1">小雅医生</Option>
                <Option value="avatar_2">培训师小李</Option>
                <Option value="avatar_3">客服小王</Option>
              </Select>
            </Form.Item>
          </Form>
        );
      case 3:
        return (
          <Form form={form} layout="vertical">
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name="enableVoiceInteraction" label="启用语音交互" valuePropName="checked">
                  <Switch />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="enableTextInteraction" label="启用文本交互" valuePropName="checked">
                  <Switch />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name="autoStart" label="自动启动" valuePropName="checked">
                  <Switch />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="maxSessionDuration" label="最大会话时长(秒)">
                  <Input type="number" placeholder="1800" />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item name="welcomeMessage" label="欢迎消息">
              <TextArea rows={3} placeholder="请输入欢迎消息" />
            </Form.Item>
          </Form>
        );
      default:
        return null;
    }
  };

  useEffect(() => {
    loadApplications();
  }, []);

  return (
    <div style={{ padding: '16px', height: '100%', overflow: 'auto' }}>
      <div style={{ marginBottom: '16px' }}>
        <Title level={4}>RAG应用管理</Title>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => setCreateModalVisible(true)}
        >
          创建应用
        </Button>
      </div>

      {/* 统计信息 */}
      <Row gutter={16} style={{ marginBottom: '16px' }}>
        <Col span={6}>
          <Card size="small">
            <Statistic
              title="应用总数"
              value={applications.length}
              prefix={<RobotOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <Statistic
              title="运行中"
              value={applications.filter(app => app.status === 'active').length}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <Statistic
              title="总会话数"
              value={applications.reduce((sum, app) => sum + app.totalSessions, 0)}
              prefix={<MessageOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <Statistic
              title="平均评分"
              value={
                applications.filter(app => app.averageRating > 0).length > 0
                  ? (applications.reduce((sum, app) => sum + app.averageRating, 0) / 
                     applications.filter(app => app.averageRating > 0).length).toFixed(1)
                  : '0'
              }
              suffix="/ 5.0"
            />
          </Card>
        </Col>
      </Row>

      {/* 应用列表 */}
      <Card title="应用列表">
        <Table
          columns={columns}
          dataSource={applications}
          rowKey="id"
          loading={loading}
          pagination={{ pageSize: 10 }}
        />
      </Card>

      {/* 创建应用模态框 */}
      <Modal
        title="创建RAG应用"
        open={createModalVisible}
        onCancel={() => {
          setCreateModalVisible(false);
          setCurrentStep(0);
          setCreateData({});
          form.resetFields();
        }}
        footer={
          <Space>
            <Button onClick={() => setCreateModalVisible(false)}>取消</Button>
            {currentStep > 0 && (
              <Button onClick={handlePrev}>上一步</Button>
            )}
            <Button type="primary" onClick={handleNext}>
              {currentStep < 3 ? '下一步' : '创建'}
            </Button>
          </Space>
        }
        width={600}
      >
        <Steps current={currentStep} style={{ marginBottom: '24px' }}>
          <Step title="基本信息" />
          <Step title="选择知识库" />
          <Step title="选择数字人" />
          <Step title="配置设置" />
        </Steps>
        
        {renderStepContent()}
      </Modal>
    </div>
  );
};

export default RAGApplicationsPanel;
