/**
 * 检查器面板组件
 */

// ⚠️ 警告: 此文件包含已弃用的 TabPane 组件
// 需要手动将 TabPane 替换为 Tabs 的 items 属性格式
// 参考: https://ant.design/components/tabs-cn#tabs-tabpane-已废弃
import React from 'react';
import { Form, Input, InputNumber, Switch, Select, Button, Collapse, Divider, Space, Typography, Tabs } from 'antd';
import { PlusOutlined, EyeOutlined, LockOutlined, CopyOutlined, DeleteOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../../store';
import TransformEditor from '../scene/TransformEditor';

const { Panel } = Collapse;
const { Option } = Select;
const { Text } = Typography;
const InspectorPanel: React.FC = () => {
  const { t } = useTranslation();
  
  const { selectedObject, selectedObjects } = useAppSelector((state) => state.editor);
  
  const [form] = Form.useForm();
  
  // 如果没有选中对象，显示空状态
  if (!selectedObject) {
    return (
      <div style={{ padding: 16, textAlign: 'center', color: '#999' }}>
        {t('editor.noObjectSelected')}
      </div>
    );
  }
  
  // 如果选中了多个对象，显示多选状态
  if (selectedObjects.length > 1) {
    return (
      <div style={{ padding: 16 }}>
        <Text>{t('editor.multipleObjectsSelected', { count: selectedObjects.length })}</Text>
        <Divider />
        <Collapse defaultActiveKey={['transform']} bordered={false}>
          <Panel header={t('editor.transform')} key="transform">
            <Form layout="vertical">
              <Form.Item label={t('editor.position')}>
                <Space.Compact style={{ width: '100%' }}>
                  <InputNumber style={{ width: '33%' }} placeholder="X" />
                  <InputNumber style={{ width: '33%' }} placeholder="Y" />
                  <InputNumber style={{ width: '33%' }} placeholder="Z" />
                </Space.Compact>
              </Form.Item>
              <Form.Item label={t('editor.rotation')}>
                <Space.Compact style={{ width: '100%' }}>
                  <InputNumber style={{ width: '33%' }} placeholder="X" />
                  <InputNumber style={{ width: '33%' }} placeholder="Y" />
                  <InputNumber style={{ width: '33%' }} placeholder="Z" />
                </Space.Compact>
              </Form.Item>
              <Form.Item label={t('editor.scale')}>
                <Space.Compact style={{ width: '100%' }}>
                  <InputNumber style={{ width: '33%' }} placeholder="X" />
                  <InputNumber style={{ width: '33%' }} placeholder="Y" />
                  <InputNumber style={{ width: '33%' }} placeholder="Z" />
                </Space.Compact>
              </Form.Item>
            </Form>
          </Panel>
        </Collapse>
      </div>
    );
  }
  
  // 单个对象的检查器
  return (
    <div style={{ height: '100%', overflow: 'auto' }}>
      <div style={{ padding: '8px 16px', borderBottom: '1px solid #f0f0f0', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <Text strong>{selectedObject.title}</Text>
          <Text type="secondary" style={{ marginLeft: 8 }}>({selectedObject.key})</Text>
        </div>
        <Space>
          <Button type="text" icon={<EyeOutlined />} size="small" />
          <Button type="text" icon={<LockOutlined />} size="small" />
          <Button type="text" icon={<CopyOutlined />} size="small" />
          <Button type="text" icon={<DeleteOutlined />} size="small" />
        </Space>
      </div>
      
      <Tabs defaultActiveKey="components" style={{ padding: '0 8px' }}>
        <TabPane tab={t('editor.components')} key="components">
          <Form form={form} layout="vertical" initialValues={{
            name: selectedObject.title,
            tag: 'Untagged',
            layer: 'Default',
            static: false,
            visible: true,
            position: { x: 0, y: 0, z: 0 },
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 1, y: 1, z: 1 }
          }}>
            <Collapse defaultActiveKey={['basic', 'transform']} bordered={false}>
              <Panel header={t('editor.basic')} key="basic">
                <Form.Item name="name" label={t('editor.name')}>
                  <Input />
                </Form.Item>
                <Form.Item name="tag" label={t('editor.tag')}>
                  <Select>
                    <Option value="Untagged">Untagged</Option>
                    <Option value="Player">Player</Option>
                    <Option value="Enemy">Enemy</Option>
                    <Option value="Environment">Environment</Option>
                  </Select>
                </Form.Item>
                <Form.Item name="layer" label={t('editor.layer')}>
                  <Select>
                    <Option value="Default">Default</Option>
                    <Option value="UI">UI</Option>
                    <Option value="Player">Player</Option>
                    <Option value="Enemy">Enemy</Option>
                  </Select>
                </Form.Item>
                <Form.Item name="static" label={t('editor.static')} valuePropName="checked">
                  <Switch />
                </Form.Item>
                <Form.Item name="visible" label={t('editor.visible')} valuePropName="checked">
                  <Switch />
                </Form.Item>
              </Panel>
              
              <Panel header={t('editor.transform')} key="transform">
                {/* 使用增强的 TransformEditor 组件 */}
                <div style={{ margin: '-12px' }}>
                  <TransformEditor />
                </div>
              </Panel>
              
              {selectedObject.key === 'cube' && (
                <Panel header={t('editor.meshRenderer')} key="meshRenderer">
                  <Form.Item label={t('editor.material')}>
                    <Select defaultValue="default">
                      <Option value="default">Default</Option>
                      <Option value="red">Red</Option>
                      <Option value="blue">Blue</Option>
                      <Option value="green">Green</Option>
                    </Select>
                  </Form.Item>
                  <Form.Item label={t('editor.castShadows')} valuePropName="checked">
                    <Switch defaultChecked />
                  </Form.Item>
                  <Form.Item label={t('editor.receiveShadows')} valuePropName="checked">
                    <Switch defaultChecked />
                  </Form.Item>
                </Panel>
              )}
              
              {selectedObject.key === 'directionalLight' && (
                <Panel header={t('editor.light')} key="light">
                  <Form.Item label={t('editor.color')}>
                    <Input type="color" defaultValue="#ffffff" />
                  </Form.Item>
                  <Form.Item label={t('editor.intensity')}>
                    <InputNumber min={0} max={10} step={0.1} defaultValue={1} />
                  </Form.Item>
                  <Form.Item label={t('editor.castShadows')} valuePropName="checked">
                    <Switch defaultChecked />
                  </Form.Item>
                  <Form.Item label={t('editor.shadowResolution')}>
                    <Select defaultValue="1024">
                      <Option value="256">256</Option>
                      <Option value="512">512</Option>
                      <Option value="1024">1024</Option>
                      <Option value="2048">2048</Option>
                    </Select>
                  </Form.Item>
                </Panel>
              )}
            </Collapse>
            
            <div style={{ padding: '16px 0' }}>
              <Button type="primary" icon={<PlusOutlined />} block>
                {t('editor.addComponent')}
              </Button>
            </div>
          </Form>
        </TabPane>
        
        <TabPane tab={t('editor.materials')} key="materials">
          <div style={{ padding: 16, textAlign: 'center', color: '#999' }}>
            {t('editor.materialsTabContent')}
          </div>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default InspectorPanel;
