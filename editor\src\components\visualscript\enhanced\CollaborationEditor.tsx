/**
 * CollaborationEditor.tsx
 * 
 * 协作编辑组件 - 支持多用户实时协作编辑
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Card,
  Avatar,
  Space,
  Tag,
  Tooltip,
  Button,
  Modal,
  List,
  Typography,
  Badge,
  Popover,
  Alert,
  Switch,
  Slider,
  Divider
} from 'antd';
import {
  UserOutlined,
  TeamOutlined,
  ShareAltOutlined,
  LockOutlined,
  UnlockOutlined,
  EyeOutlined,
  EditOutlined,
  MessageOutlined,
  SettingOutlined,
  WifiOutlined,
  DisconnectOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Title, Text } = Typography;

/**
 * 用户信息接口
 */
export interface User {
  id: string;
  name: string;
  avatar?: string;
  email?: string;
  role: 'owner' | 'editor' | 'viewer';
  color: string;
  isOnline: boolean;
  lastSeen?: string;
}

/**
 * 光标位置接口
 */
export interface CursorPosition {
  userId: string;
  nodeId?: string;
  x: number;
  y: number;
  timestamp: number;
}

/**
 * 编辑操作接口
 */
export interface EditOperation {
  id: string;
  type: 'create' | 'update' | 'delete' | 'move' | 'connect';
  userId: string;
  timestamp: number;
  data: any;
  nodeId?: string;
  connectionId?: string;
}

/**
 * 编辑冲突接口
 */
export interface EditConflict {
  id: string;
  type: 'concurrent_edit' | 'version_mismatch' | 'permission_denied';
  operations: EditOperation[];
  resolution?: 'merge' | 'override' | 'reject';
  timestamp: number;
}

/**
 * 协作状态接口
 */
export interface CollaborationState {
  isConnected: boolean;
  users: User[];
  cursors: CursorPosition[];
  activeOperations: EditOperation[];
  conflicts: EditConflict[];
  permissions: Record<string, string[]>;
}

/**
 * 协作编辑组件属性
 */
export interface CollaborationEditorProps {
  /** 脚本ID */
  scriptId: string;
  /** 当前用户ID */
  userId: string;
  /** WebSocket连接URL */
  websocketUrl: string;
  /** 是否启用协作 */
  enabled?: boolean;
  /** 协作者加入回调 */
  onCollaboratorJoin?: (user: User) => void;
  /** 协作者离开回调 */
  onCollaboratorLeave?: (userId: string) => void;
  /** 编辑操作回调 */
  onEditOperation?: (operation: EditOperation) => void;
  /** 冲突解决回调 */
  onConflictResolution?: (conflict: EditConflict) => void;
  /** 权限变更回调 */
  onPermissionChange?: (userId: string, permissions: string[]) => void;
}

/**
 * 协作编辑组件
 */
const CollaborationEditor: React.FC<CollaborationEditorProps> = ({
  scriptId,
  userId,
  websocketUrl,
  enabled = true,
  onCollaboratorJoin,
  onCollaboratorLeave,
  onEditOperation,
  onConflictResolution,
  onPermissionChange
}) => {
  const { t } = useTranslation();
  
  // 状态管理
  const [collaborationState, setCollaborationState] = useState<CollaborationState>({
    isConnected: false,
    users: [],
    cursors: [],
    activeOperations: [],
    conflicts: [],
    permissions: {}
  });
  
  // 模态框状态
  const [shareModalVisible, setShareModalVisible] = useState(false);
  const [conflictModalVisible, setConflictModalVisible] = useState(false);
  const [settingsModalVisible, setSettingsModalVisible] = useState(false);
  
  // 设置状态
  const [showCursors, setShowCursors] = useState(true);
  const [showOperations, setShowOperations] = useState(true);
  const [cursorOpacity, setCursorOpacity] = useState(0.8);
  
  // WebSocket引用
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  /**
   * 建立WebSocket连接
   */
  const connectWebSocket = useCallback(() => {
    if (!enabled || wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    try {
      const ws = new WebSocket(`${websocketUrl}?scriptId=${scriptId}&userId=${userId}`);
      
      ws.onopen = () => {
        console.log('协作连接已建立');
        setCollaborationState(prev => ({ ...prev, isConnected: true }));
        
        // 清除重连定时器
        if (reconnectTimeoutRef.current) {
          clearTimeout(reconnectTimeoutRef.current);
          reconnectTimeoutRef.current = null;
        }
      };
      
      ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          handleWebSocketMessage(message);
        } catch (error) {
          console.error('解析协作消息失败:', error);
        }
      };
      
      ws.onclose = () => {
        console.log('协作连接已断开');
        setCollaborationState(prev => ({ ...prev, isConnected: false }));
        
        // 自动重连
        if (enabled) {
          reconnectTimeoutRef.current = setTimeout(connectWebSocket, 3000);
        }
      };
      
      ws.onerror = (error) => {
        console.error('协作连接错误:', error);
      };
      
      wsRef.current = ws;
    } catch (error) {
      console.error('建立协作连接失败:', error);
    }
  }, [enabled, websocketUrl, scriptId, userId]);

  /**
   * 处理WebSocket消息
   */
  const handleWebSocketMessage = useCallback((message: any) => {
    switch (message.type) {
      case 'user_joined':
        setCollaborationState(prev => ({
          ...prev,
          users: [...prev.users.filter(u => u.id !== message.user.id), message.user]
        }));
        if (onCollaboratorJoin) {
          onCollaboratorJoin(message.user);
        }
        break;
        
      case 'user_left':
        setCollaborationState(prev => ({
          ...prev,
          users: prev.users.filter(u => u.id !== message.userId),
          cursors: prev.cursors.filter(c => c.userId !== message.userId)
        }));
        if (onCollaboratorLeave) {
          onCollaboratorLeave(message.userId);
        }
        break;
        
      case 'cursor_update':
        setCollaborationState(prev => ({
          ...prev,
          cursors: [
            ...prev.cursors.filter(c => c.userId !== message.cursor.userId),
            message.cursor
          ]
        }));
        break;
        
      case 'edit_operation':
        setCollaborationState(prev => ({
          ...prev,
          activeOperations: [...prev.activeOperations, message.operation]
        }));
        if (onEditOperation) {
          onEditOperation(message.operation);
        }
        break;
        
      case 'conflict_detected':
        setCollaborationState(prev => ({
          ...prev,
          conflicts: [...prev.conflicts, message.conflict]
        }));
        setConflictModalVisible(true);
        break;
        
      case 'permission_updated':
        setCollaborationState(prev => ({
          ...prev,
          permissions: {
            ...prev.permissions,
            [message.userId]: message.permissions
          }
        }));
        break;
        
      default:
        console.warn('未知的协作消息类型:', message.type);
    }
  }, [onCollaboratorJoin, onCollaboratorLeave, onEditOperation]);

  /**
   * 发送WebSocket消息
   */
  const sendWebSocketMessage = useCallback((message: any) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
    }
  }, []);

  /**
   * 更新光标位置
   */
  const updateCursorPosition = useCallback((x: number, y: number, nodeId?: string) => {
    const cursor: CursorPosition = {
      userId,
      nodeId,
      x,
      y,
      timestamp: Date.now()
    };
    
    sendWebSocketMessage({
      type: 'cursor_update',
      cursor
    });
  }, [userId, sendWebSocketMessage]);

  /**
   * 发送编辑操作
   */
  const sendEditOperation = useCallback((operation: Omit<EditOperation, 'id' | 'userId' | 'timestamp'>) => {
    const fullOperation: EditOperation = {
      id: `op_${Date.now()}_${Math.random()}`,
      userId,
      timestamp: Date.now(),
      ...operation
    };
    
    sendWebSocketMessage({
      type: 'edit_operation',
      operation: fullOperation
    });
  }, [userId, sendWebSocketMessage]);

  /**
   * 解决冲突
   */
  const resolveConflict = useCallback((conflictId: string, resolution: 'merge' | 'override' | 'reject') => {
    const conflict = collaborationState.conflicts.find(c => c.id === conflictId);
    if (!conflict) return;

    const resolvedConflict: EditConflict = {
      ...conflict,
      resolution
    };

    sendWebSocketMessage({
      type: 'resolve_conflict',
      conflict: resolvedConflict
    });

    setCollaborationState(prev => ({
      ...prev,
      conflicts: prev.conflicts.filter(c => c.id !== conflictId)
    }));

    if (onConflictResolution) {
      onConflictResolution(resolvedConflict);
    }
  }, [collaborationState.conflicts, sendWebSocketMessage, onConflictResolution]);

  /**
   * 邀请协作者
   */
  const inviteCollaborator = useCallback((email: string, role: 'editor' | 'viewer') => {
    sendWebSocketMessage({
      type: 'invite_user',
      email,
      role
    });
  }, [sendWebSocketMessage]);

  // 建立连接
  useEffect(() => {
    if (enabled) {
      connectWebSocket();
    }

    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, [enabled, connectWebSocket]);

  /**
   * 渲染协作者列表
   */
  const renderCollaborators = () => {
    const onlineUsers = collaborationState.users.filter(u => u.isOnline);
    const offlineUsers = collaborationState.users.filter(u => !u.isOnline);

    return (
      <Space direction="vertical" style={{ width: '100%' }}>
        {onlineUsers.length > 0 && (
          <div>
            <Text strong>{t('协作.在线用户')} ({onlineUsers.length})</Text>
            <Space wrap style={{ marginTop: 8 }}>
              {onlineUsers.map(user => (
                <Tooltip key={user.id} title={`${user.name} (${t(`协作.角色.${user.role}`)})`}>
                  <Badge dot color={user.color}>
                    <Avatar
                      src={user.avatar}
                      icon={<UserOutlined />}
                      style={{ backgroundColor: user.color }}
                    >
                      {!user.avatar && user.name.charAt(0).toUpperCase()}
                    </Avatar>
                  </Badge>
                </Tooltip>
              ))}
            </Space>
          </div>
        )}
        
        {offlineUsers.length > 0 && (
          <div>
            <Text type="secondary">{t('协作.离线用户')} ({offlineUsers.length})</Text>
            <Space wrap style={{ marginTop: 8 }}>
              {offlineUsers.map(user => (
                <Tooltip key={user.id} title={`${user.name} - ${t('协作.最后在线')}: ${user.lastSeen}`}>
                  <Avatar
                    src={user.avatar}
                    icon={<UserOutlined />}
                    style={{ opacity: 0.5 }}
                  >
                    {!user.avatar && user.name.charAt(0).toUpperCase()}
                  </Avatar>
                </Tooltip>
              ))}
            </Space>
          </div>
        )}
      </Space>
    );
  };

  /**
   * 渲染连接状态
   */
  const renderConnectionStatus = () => (
    <Space>
      {collaborationState.isConnected ? (
        <Tag icon={<WifiOutlined />} color="success">
          {t('协作.已连接')}
        </Tag>
      ) : (
        <Tag icon={<DisconnectOutlined />} color="error">
          {t('协作.已断开')}
        </Tag>
      )}
      
      <Text type="secondary">
        {collaborationState.users.filter(u => u.isOnline).length} {t('协作.在线')}
      </Text>
    </Space>
  );

  /**
   * 渲染冲突解决模态框
   */
  const renderConflictModal = () => (
    <Modal
      title={t('协作.解决冲突')}
      open={conflictModalVisible}
      onCancel={() => setConflictModalVisible(false)}
      footer={null}
      width={600}
    >
      <List
        dataSource={collaborationState.conflicts}
        renderItem={(conflict) => (
          <List.Item
            actions={[
              <Button
                key="merge"
                type="primary"
                onClick={() => resolveConflict(conflict.id, 'merge')}
              >
                {t('协作.合并')}
              </Button>,
              <Button
                key="override"
                onClick={() => resolveConflict(conflict.id, 'override')}
              >
                {t('协作.覆盖')}
              </Button>,
              <Button
                key="reject"
                danger
                onClick={() => resolveConflict(conflict.id, 'reject')}
              >
                {t('协作.拒绝')}
              </Button>
            ]}
          >
            <List.Item.Meta
              title={t(`协作.冲突类型.${conflict.type}`)}
              description={
                <div>
                  <Text>{t('协作.涉及操作')}: {conflict.operations.length}</Text>
                  <br />
                  <Text type="secondary">
                    {new Date(conflict.timestamp).toLocaleString()}
                  </Text>
                </div>
              }
            />
          </List.Item>
        )}
      />
    </Modal>
  );

  if (!enabled) {
    return (
      <Alert
        message={t('协作.已禁用')}
        description={t('协作.禁用说明')}
        type="info"
        showIcon
      />
    );
  }

  return (
    <div className="collaboration-editor">
      <Card
        size="small"
        title={
          <Space>
            <TeamOutlined />
            <Title level={5} style={{ margin: 0 }}>
              {t('协作.协作编辑')}
            </Title>
          </Space>
        }
        extra={
          <Space>
            {renderConnectionStatus()}
            <Button
              icon={<ShareAltOutlined />}
              size="small"
              onClick={() => setShareModalVisible(true)}
            >
              {t('协作.分享')}
            </Button>
            <Button
              icon={<SettingOutlined />}
              size="small"
              onClick={() => setSettingsModalVisible(true)}
            />
          </Space>
        }
      >
        {renderCollaborators()}
        
        {collaborationState.conflicts.length > 0 && (
          <Alert
            message={t('协作.存在冲突')}
            description={t('协作.冲突提示', { count: collaborationState.conflicts.length })}
            type="warning"
            action={
              <Button
                size="small"
                onClick={() => setConflictModalVisible(true)}
              >
                {t('协作.查看冲突')}
              </Button>
            }
            style={{ marginTop: 16 }}
          />
        )}
      </Card>
      
      {renderConflictModal()}
      
      {/* 分享模态框 */}
      <Modal
        title={t('协作.分享脚本')}
        open={shareModalVisible}
        onCancel={() => setShareModalVisible(false)}
        footer={null}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <Text>{t('协作.分享链接')}</Text>
          <Input.Group compact>
            <Input
              value={`${window.location.origin}/script/${scriptId}`}
              readOnly
              style={{ width: 'calc(100% - 80px)' }}
            />
            <Button
              onClick={() => {
                navigator.clipboard.writeText(`${window.location.origin}/script/${scriptId}`);
                message.success(t('协作.链接已复制'));
              }}
            >
              {t('协作.复制')}
            </Button>
          </Input.Group>
        </Space>
      </Modal>
      
      {/* 设置模态框 */}
      <Modal
        title={t('协作.协作设置')}
        open={settingsModalVisible}
        onCancel={() => setSettingsModalVisible(false)}
        footer={null}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text>{t('协作.显示光标')}</Text>
            <Switch
              checked={showCursors}
              onChange={setShowCursors}
              style={{ marginLeft: 16 }}
            />
          </div>
          
          <div>
            <Text>{t('协作.显示操作')}</Text>
            <Switch
              checked={showOperations}
              onChange={setShowOperations}
              style={{ marginLeft: 16 }}
            />
          </div>
          
          <div>
            <Text>{t('协作.光标透明度')}</Text>
            <Slider
              min={0.1}
              max={1}
              step={0.1}
              value={cursorOpacity}
              onChange={setCursorOpacity}
              style={{ marginTop: 8 }}
            />
          </div>
        </Space>
      </Modal>
    </div>
  );
};

export default CollaborationEditor;
