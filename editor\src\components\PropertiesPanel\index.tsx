/**
 * 属性面板组件
 */

// ⚠️ 警告: 此文件包含已弃用的 TabPane 组件
// 需要手动将 TabPane 替换为 Tabs 的 items 属性格式
// 参考: https://ant.design/components/tabs-cn#tabs-tabpane-已废弃
import React from 'react';
import { Tabs, Form, Input, InputNumber, Switch, Select, Button, Collapse } from 'antd';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import { updateEntity } from '../../store/scene/sceneSlice';
import TransformEditor from '../scene/TransformEditor';
import './PropertiesPanel.less';

const { Panel } = Collapse;
const { Option } = Select;

const PropertiesPanel: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { entities, selectedEntityId } = useSelector((state: RootState) => state.scene);
  const [form] = Form.useForm();
  
  // 获取选中的实体
  const selectedEntity = entities.find(entity => entity.id === selectedEntityId);
  
  // 如果没有选中实体，显示空面板
  if (!selectedEntity) {
    return (
      <div className="properties-panel empty-panel">
        <p>{t('editor.properties.noSelection')}</p>
      </div>
    );
  }
  
  // 处理表单值变化
  const handleValuesChange = (changedValues: any) => {
    if (!selectedEntityId) return;

    // 处理变换属性
    if (changedValues.transform) {
      const { position, rotation, scale } = changedValues.transform;

      const changes: any = { transform: { ...selectedEntity.transform } };

      if (position) changes.transform.position = position;
      if (rotation) changes.transform.rotation = rotation;
      if (scale) changes.transform.scale = scale;

      dispatch(updateEntity({
        id: selectedEntityId,
        changes
      }));
    }

    // 处理基本属性
    if (changedValues.name || changedValues.visible !== undefined || changedValues.locked !== undefined) {
      dispatch(updateEntity({
        id: selectedEntityId,
        changes: {
          ...changedValues
        }
      }));
    }

    // 处理组件属性
    if (changedValues.components) {
      dispatch(updateEntity({
        id: selectedEntityId,
        changes: {
          components: {
            ...selectedEntity.components,
            ...changedValues.components
          }
        }
      }));
    }
  };
  
  // 添加组件
  const handleAddComponent = (componentType: string) => {
    if (!selectedEntityId) return;

    const componentDefaults: any = {
      material: {
        color: '#ffffff',
        metalness: 0,
        roughness: 1,
        emissive: '#000000'
      },
      light: {
        type: 'point',
        color: '#ffffff',
        intensity: 1,
        distance: 0,
        decay: 2
      },
      physics: {
        type: 'static',
        mass: 0,
        restitution: 0.3,
        friction: 0.5
      },
      audio: {
        src: '',
        volume: 1,
        loop: false,
        autoplay: false
      }
    };

    const newComponents = {
      ...selectedEntity.components,
      [componentType]: componentDefaults[componentType]
    };

    dispatch(updateEntity({
      id: selectedEntityId,
      changes: {
        components: newComponents
      }
    }));
  };

  // 删除组件
  const handleRemoveComponent = (componentType: string) => {
    if (!selectedEntityId) return;

    const newComponents = { ...selectedEntity.components };
    delete newComponents[componentType];

    dispatch(updateEntity({
      id: selectedEntityId,
      changes: {
        components: newComponents
      }
    }));
  };
  
  return (
    <div className="properties-panel">
      <h3>{t('editor.panels.properties')}</h3>
      
      <Form
        form={form}
        layout="vertical"
        initialValues={selectedEntity}
        onValuesChange={handleValuesChange}
      >
        <Tabs defaultActiveKey="basic">
          <TabPane tab={t('editor.properties.basic')} key="basic">
            <Form.Item name="name" label={t('editor.properties.name')}>
              <Input />
            </Form.Item>
            
            <Form.Item name="type" label={t('editor.properties.type')}>
              <Select disabled>
                <Option value="mesh">{t('editor.properties.types.mesh')}</Option>
                <Option value="light">{t('editor.properties.types.light')}</Option>
                <Option value="camera">{t('editor.properties.types.camera')}</Option>
                <Option value="group">{t('editor.properties.types.group')}</Option>
              </Select>
            </Form.Item>
            
            <Form.Item name="visible" label={t('editor.properties.visible')} valuePropName="checked">
              <Switch />
            </Form.Item>
            
            <Form.Item name="locked" label={t('editor.properties.locked')} valuePropName="checked">
              <Switch />
            </Form.Item>
          </TabPane>
          
          <TabPane tab={t('editor.properties.transform')} key="transform">
            {/* 使用增强的 TransformEditor 组件 */}
            <div style={{ margin: '-16px' }}>
              <TransformEditor
                entityId={selectedEntityId || undefined}
                onChange={(transformData) => {
                  if (!selectedEntityId) return;

                  // 将变换数据转换为数组格式以兼容现有的状态结构
                  const transformArray = {
                    position: [transformData.position.x, transformData.position.y, transformData.position.z] as [number, number, number],
                    rotation: [transformData.rotation.x, transformData.rotation.y, transformData.rotation.z] as [number, number, number],
                    scale: [transformData.scale.x, transformData.scale.y, transformData.scale.z] as [number, number, number]
                  };

                  dispatch(updateEntity({
                    id: selectedEntityId,
                    changes: {
                      transform: transformArray
                    }
                  }));
                }}
              />
            </div>
          </TabPane>
          
          <TabPane tab={t('editor.properties.components')} key="components">
            {/* 组件列表 */}
            <Collapse>
              {Object.keys(selectedEntity.components || {}).map(componentType => (
                <Panel 
                  header={t(`editor.components.${componentType}`)} 
                  key={componentType}
                  extra={
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleRemoveComponent(componentType);
                      }}
                    />
                  }
                >
                  {/* 根据组件类型渲染不同的表单 */}
                  {componentType === 'material' && (
                    <>
                      <Form.Item name={['components', 'material', 'color']} label={t('editor.components.material.color')}>
                        <Input type="color" />
                      </Form.Item>
                      <Form.Item name={['components', 'material', 'metalness']} label={t('editor.components.material.metalness')}>
                        <InputNumber min={0} max={1} step={0.1} style={{ width: '100%' }} />
                      </Form.Item>
                      <Form.Item name={['components', 'material', 'roughness']} label={t('editor.components.material.roughness')}>
                        <InputNumber min={0} max={1} step={0.1} style={{ width: '100%' }} />
                      </Form.Item>
                    </>
                  )}
                  
                  {componentType === 'light' && (
                    <>
                      <Form.Item name={['components', 'light', 'type']} label={t('editor.components.light.type')}>
                        <Select>
                          <Option value="point">{t('editor.components.light.types.point')}</Option>
                          <Option value="spot">{t('editor.components.light.types.spot')}</Option>
                          <Option value="directional">{t('editor.components.light.types.directional')}</Option>
                        </Select>
                      </Form.Item>
                      <Form.Item name={['components', 'light', 'color']} label={t('editor.components.light.color')}>
                        <Input type="color" />
                      </Form.Item>
                      <Form.Item name={['components', 'light', 'intensity']} label={t('editor.components.light.intensity')}>
                        <InputNumber min={0} step={0.1} style={{ width: '100%' }} />
                      </Form.Item>
                    </>
                  )}
                  
                  {componentType === 'physics' && (
                    <>
                      <Form.Item name={['components', 'physics', 'type']} label={t('editor.components.physics.type')}>
                        <Select>
                          <Option value="static">{t('editor.components.physics.types.static')}</Option>
                          <Option value="dynamic">{t('editor.components.physics.types.dynamic')}</Option>
                          <Option value="kinematic">{t('editor.components.physics.types.kinematic')}</Option>
                        </Select>
                      </Form.Item>
                      <Form.Item name={['components', 'physics', 'mass']} label={t('editor.components.physics.mass')}>
                        <InputNumber min={0} style={{ width: '100%' }} />
                      </Form.Item>
                    </>
                  )}
                </Panel>
              ))}
            </Collapse>
            
            {/* 添加组件按钮 */}
            <div className="add-component">
              <Select
                placeholder={t('editor.properties.addComponent')}
                style={{ width: '70%' }}
                onChange={handleAddComponent}
                value={null}
              >
                <Option value="material">{t('editor.components.material')}</Option>
                <Option value="light">{t('editor.components.light')}</Option>
                <Option value="physics">{t('editor.components.physics')}</Option>
                <Option value="audio">{t('editor.components.audio')}</Option>
              </Select>
              <Button type="primary" icon={<PlusOutlined />}>
                {t('editor.properties.add')}
              </Button>
            </div>
          </TabPane>
        </Tabs>
      </Form>
    </div>
  );
};

export default PropertiesPanel;
