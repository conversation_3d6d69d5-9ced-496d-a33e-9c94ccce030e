/**
 * ComponentMarket.tsx
 * 
 * 组件市场主界面
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Layout,
  Input,
  Select,
  Card,
  Button,
  Space,
  Tag,
  Rate,
  Avatar,
  Pagination,
  Spin,
  Empty,
  Tabs,
  Statistic,
  Row,
  Col,
  Badge,
  Tooltip,
  message
} from 'antd';
import {
  SearchOutlined,
  DownloadOutlined,
  EyeOutlined,
  StarOutlined,
  UserOutlined,
  AppstoreOutlined,
  FireOutlined,
  ClockCircleOutlined,
  TrophyOutlined
} from '@ant-design/icons';
import { ComponentMarketService } from '../../services/ComponentMarketService';
import { ComponentDetailModal } from './ComponentDetailModal';
import { PublishComponentModal } from './PublishComponentModal';
import './ComponentMarket.module.css';

const { Header, Content, Sider } = Layout;
const { Search } = Input;
const { TabPane } = Tabs;

/**
 * 组件数据接口
 */
export interface ComponentData {
  id: string;
  name: string;
  description: string;
  category: string;
  tags: string[];
  version: string;
  author: {
    id: string;
    username: string;
    avatar?: string;
  };
  downloads: number;
  rating: number;
  ratingCount: number;
  previewImage?: string;
  createdAt: string;
  updatedAt: string;
  isPopular?: boolean;
  isFeatured?: boolean;
}

/**
 * 搜索参数接口
 */
export interface SearchParams {
  keyword?: string;
  category?: string;
  tags?: string[];
  sortBy?: 'downloads' | 'rating' | 'created' | 'updated';
  sortOrder?: 'ASC' | 'DESC';
  page?: number;
  limit?: number;
}

/**
 * 组件市场属性
 */
export interface ComponentMarketProps {
  /** 是否显示发布按钮 */
  showPublishButton?: boolean;
  /** 组件选择回调 */
  onComponentSelect?: (component: ComponentData) => void;
  /** 样式类名 */
  className?: string;
}

/**
 * 组件市场组件
 */
export const ComponentMarket: React.FC<ComponentMarketProps> = ({
  showPublishButton = true,
  onComponentSelect,
  className
}) => {
  const [components, setComponents] = useState<ComponentData[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchParams, setSearchParams] = useState<SearchParams>({
    sortBy: 'downloads',
    sortOrder: 'DESC',
    page: 1,
    limit: 20
  });
  const [total, setTotal] = useState(0);
  const [categories, setCategories] = useState<string[]>([]);
  const [popularTags, setPopularTags] = useState<Array<{ tag: string; count: number }>>([]);
  const [stats, setStats] = useState<any>({});
  const [selectedComponent, setSelectedComponent] = useState<ComponentData | null>(null);
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false);
  const [isPublishModalVisible, setIsPublishModalVisible] = useState(false);
  const [activeTab, setActiveTab] = useState('all');

  const marketService = useMemo(() => new ComponentMarketService(), []);

  // 加载组件列表
  const loadComponents = useCallback(async (params: SearchParams = searchParams) => {
    setLoading(true);
    try {
      const response = await marketService.searchComponents(params);
      if (response.success) {
        setComponents(response.data.components);
        setTotal(response.data.total);
      } else {
        message.error(response.message || '加载组件失败');
      }
    } catch (error) {
      message.error('网络错误，请稍后重试');
    } finally {
      setLoading(false);
    }
  }, [marketService, searchParams]);

  // 加载统计信息
  const loadStats = useCallback(async () => {
    try {
      const response = await marketService.getStats();
      if (response.success) {
        setStats(response.data);
        setCategories(Object.keys(response.data.categoryCounts));
        setPopularTags(response.data.popularTags.slice(0, 20));
      }
    } catch (error) {
      console.error('加载统计信息失败:', error);
    }
  }, [marketService]);

  // 初始化
  useEffect(() => {
    loadComponents();
    loadStats();
  }, [loadComponents, loadStats]);

  // 处理搜索
  const handleSearch = useCallback((keyword: string) => {
    const newParams = { ...searchParams, keyword, page: 1 };
    setSearchParams(newParams);
    loadComponents(newParams);
  }, [searchParams, loadComponents]);

  // 处理分类筛选
  const handleCategoryChange = useCallback((category: string) => {
    const newParams = { ...searchParams, category: category === 'all' ? undefined : category, page: 1 };
    setSearchParams(newParams);
    loadComponents(newParams);
  }, [searchParams, loadComponents]);

  // 处理标签筛选
  const handleTagClick = useCallback((tag: string) => {
    const currentTags = searchParams.tags || [];
    const newTags = currentTags.includes(tag)
      ? currentTags.filter(t => t !== tag)
      : [...currentTags, tag];
    
    const newParams = { ...searchParams, tags: newTags, page: 1 };
    setSearchParams(newParams);
    loadComponents(newParams);
  }, [searchParams, loadComponents]);

  // 处理排序
  const handleSortChange = useCallback((sortBy: string) => {
    const newParams = { ...searchParams, sortBy: sortBy as any, page: 1 };
    setSearchParams(newParams);
    loadComponents(newParams);
  }, [searchParams, loadComponents]);

  // 处理分页
  const handlePageChange = useCallback((page: number, pageSize?: number) => {
    const newParams = { ...searchParams, page, limit: pageSize || searchParams.limit };
    setSearchParams(newParams);
    loadComponents(newParams);
  }, [searchParams, loadComponents]);

  // 处理组件下载
  const handleDownload = useCallback(async (component: ComponentData) => {
    try {
      const response = await marketService.downloadComponent(component.id);
      if (response.success) {
        message.success('下载成功');
        // 更新下载计数
        setComponents(prev => prev.map(c => 
          c.id === component.id ? { ...c, downloads: c.downloads + 1 } : c
        ));
      } else {
        message.error(response.message || '下载失败');
      }
    } catch (error) {
      message.error('下载失败，请稍后重试');
    }
  }, [marketService]);

  // 处理组件详情
  const handleViewDetail = useCallback((component: ComponentData) => {
    setSelectedComponent(component);
    setIsDetailModalVisible(true);
  }, []);

  // 处理组件选择
  const handleSelectComponent = useCallback((component: ComponentData) => {
    onComponentSelect?.(component);
    message.success(`已选择组件: ${component.name}`);
  }, [onComponentSelect]);

  // 加载特定类型的组件
  const loadSpecialComponents = useCallback(async (type: 'popular' | 'latest' | 'featured') => {
    setLoading(true);
    try {
      let response;
      switch (type) {
        case 'popular':
          response = await marketService.getPopularComponents(20);
          break;
        case 'latest':
          response = await marketService.getLatestComponents(20);
          break;
        default:
          response = await marketService.searchComponents({ 
            sortBy: 'downloads', 
            sortOrder: 'DESC',
            limit: 20 
          });
      }
      
      if (response.success) {
        setComponents(Array.isArray(response.data) ? response.data : response.data.components);
        setTotal(Array.isArray(response.data) ? response.data.length : response.data.total);
      }
    } catch (error) {
      message.error('加载失败');
    } finally {
      setLoading(false);
    }
  }, [marketService]);

  // 处理标签页切换
  const handleTabChange = useCallback((key: string) => {
    setActiveTab(key);
    if (key === 'all') {
      loadComponents({ ...searchParams, page: 1 });
    } else {
      loadSpecialComponents(key as any);
    }
  }, [loadComponents, loadSpecialComponents, searchParams]);

  // 渲染组件卡片
  const renderComponentCard = useCallback((component: ComponentData) => (
    <Card
      key={component.id}
      className="component-card"
      cover={
        component.previewImage ? (
          <img
            alt={component.name}
            src={component.previewImage}
            style={{ height: 160, objectFit: 'cover' }}
          />
        ) : (
          <div className="placeholder-cover">
            <AppstoreOutlined style={{ fontSize: 48, color: '#ccc' }} />
          </div>
        )
      }
      actions={[
        <Tooltip title="查看详情">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(component)}
          />
        </Tooltip>,
        <Tooltip title="下载组件">
          <Button
            type="text"
            icon={<DownloadOutlined />}
            onClick={() => handleDownload(component)}
          />
        </Tooltip>,
        onComponentSelect && (
          <Tooltip title="选择组件">
            <Button
              type="text"
              icon={<StarOutlined />}
              onClick={() => handleSelectComponent(component)}
            />
          </Tooltip>
        )
      ].filter(Boolean)}
    >
      <Card.Meta
        title={
          <div className="card-title">
            <span>{component.name}</span>
            {component.isFeatured && <Badge count="精选" color="gold" />}
            {component.isPopular && <Badge count="热门" color="red" />}
          </div>
        }
        description={
          <div className="card-description">
            <p className="description-text">{component.description}</p>
            <div className="card-meta">
              <Space>
                <Avatar
                  size="small"
                  src={component.author.avatar}
                  icon={<UserOutlined />}
                />
                <span className="author-name">{component.author.username}</span>
              </Space>
              <div className="card-stats">
                <Space>
                  <span>
                    <DownloadOutlined /> {component.downloads}
                  </span>
                  <Rate
                    disabled
                    allowHalf
                    value={component.rating}
                    style={{ fontSize: 12 }}
                  />
                  <span>({component.ratingCount})</span>
                </Space>
              </div>
            </div>
            <div className="card-tags">
              {component.tags.slice(0, 3).map(tag => (
                <Tag
                  key={tag}
                  size="small"
                  onClick={() => handleTagClick(tag)}
                  style={{ cursor: 'pointer' }}
                >
                  {tag}
                </Tag>
              ))}
              {component.tags.length > 3 && (
                <Tag size="small">+{component.tags.length - 3}</Tag>
              )}
            </div>
          </div>
        }
      />
    </Card>
  ), [handleViewDetail, handleDownload, handleSelectComponent, handleTagClick, onComponentSelect]);

  return (
    <Layout className={`component-market ${className || ''}`}>
      {/* 侧边栏 */}
      <Sider width={280} className="market-sider">
        <div className="sider-content">
          {/* 统计信息 */}
          <Card size="small" title="市场统计" className="stats-card">
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="组件总数"
                  value={stats.totalComponents}
                  prefix={<AppstoreOutlined />}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="总下载量"
                  value={stats.totalDownloads}
                  prefix={<DownloadOutlined />}
                />
              </Col>
            </Row>
          </Card>

          {/* 分类筛选 */}
          <Card size="small" title="分类" className="filter-card">
            <div className="category-list">
              <div
                className={`category-item ${!searchParams.category ? 'active' : ''}`}
                onClick={() => handleCategoryChange('all')}
              >
                全部分类
              </div>
              {categories.map(category => (
                <div
                  key={category}
                  className={`category-item ${searchParams.category === category ? 'active' : ''}`}
                  onClick={() => handleCategoryChange(category)}
                >
                  {category}
                  <span className="category-count">
                    ({stats.categoryCounts?.[category] || 0})
                  </span>
                </div>
              ))}
            </div>
          </Card>

          {/* 热门标签 */}
          <Card size="small" title="热门标签" className="filter-card">
            <div className="tag-cloud">
              {popularTags.map(({ tag, count }) => (
                <Tag
                  key={tag}
                  className={`tag-item ${searchParams.tags?.includes(tag) ? 'active' : ''}`}
                  onClick={() => handleTagClick(tag)}
                >
                  {tag} ({count})
                </Tag>
              ))}
            </div>
          </Card>
        </div>
      </Sider>

      {/* 主内容区 */}
      <Layout>
        {/* 头部 */}
        <Header className="market-header">
          <div className="header-content">
            <div className="search-section">
              <Search
                placeholder="搜索组件..."
                allowClear
                enterButton={<SearchOutlined />}
                size="large"
                onSearch={handleSearch}
                style={{ width: 400 }}
              />
              <Select
                value={searchParams.sortBy}
                onChange={handleSortChange}
                style={{ width: 120, marginLeft: 16 }}
              >
                <Select.Option value="downloads">下载量</Select.Option>
                <Select.Option value="rating">评分</Select.Option>
                <Select.Option value="created">最新</Select.Option>
                <Select.Option value="updated">更新</Select.Option>
              </Select>
            </div>
            
            {showPublishButton && (
              <Button
                type="primary"
                onClick={() => setIsPublishModalVisible(true)}
              >
                发布组件
              </Button>
            )}
          </div>
        </Header>

        {/* 内容区 */}
        <Content className="market-content">
          <Tabs activeKey={activeTab} onChange={handleTabChange} className="market-tabs">
            <TabPane
              tab={
                <span>
                  <AppstoreOutlined />
                  全部组件
                </span>
              }
              key="all"
            />
            <TabPane
              tab={
                <span>
                  <FireOutlined />
                  热门组件
                </span>
              }
              key="popular"
            />
            <TabPane
              tab={
                <span>
                  <ClockCircleOutlined />
                  最新组件
                </span>
              }
              key="latest"
            />
            <TabPane
              tab={
                <span>
                  <TrophyOutlined />
                  精选组件
                </span>
              }
              key="featured"
            />
          </Tabs>

          <Spin spinning={loading}>
            {components.length > 0 ? (
              <>
                <div className="components-grid">
                  {components.map(renderComponentCard)}
                </div>
                
                <div className="pagination-container">
                  <Pagination
                    current={searchParams.page}
                    total={total}
                    pageSize={searchParams.limit}
                    showSizeChanger
                    showQuickJumper
                    showTotal={(total, range) => 
                      `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                    }
                    onChange={handlePageChange}
                  />
                </div>
              </>
            ) : (
              <Empty
                description="暂无组件"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            )}
          </Spin>
        </Content>
      </Layout>

      {/* 组件详情模态框 */}
      <ComponentDetailModal
        visible={isDetailModalVisible}
        component={selectedComponent}
        onClose={() => {
          setIsDetailModalVisible(false);
          setSelectedComponent(null);
        }}
        onDownload={handleDownload}
        onSelect={onComponentSelect ? handleSelectComponent : undefined}
      />

      {/* 发布组件模态框 */}
      <PublishComponentModal
        visible={isPublishModalVisible}
        onClose={() => setIsPublishModalVisible(false)}
        onSuccess={() => {
          setIsPublishModalVisible(false);
          loadComponents();
          message.success('组件发布成功');
        }}
      />
    </Layout>
  );
};

export default ComponentMarket;
