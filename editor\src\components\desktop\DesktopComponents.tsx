/**
 * DesktopComponents.tsx
 * 
 * 桌面端特定组件集合
 */

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { 
  Menu, 
  Dropdown, 
  Tooltip, 
  Popover, 
  Modal, 
  Drawer, 
  Button, 
  Input,
  Tree,
  Table,
  Tabs
} from 'antd';
import {
  MenuOutlined,
  MoreOutlined,
  SettingOutlined,
  SearchOutlined,
  FolderOutlined,
  FileOutlined,
  CloseOutlined,
  MinusOutlined,
  BorderOutlined
} from '@ant-design/icons';
import './DesktopComponents.module.css';

/**
 * 桌面端窗口组件
 */
export interface DesktopWindowProps {
  title: string;
  children: React.ReactNode;
  width?: number;
  height?: number;
  x?: number;
  y?: number;
  resizable?: boolean;
  minimizable?: boolean;
  maximizable?: boolean;
  closable?: boolean;
  onClose?: () => void;
  onMinimize?: () => void;
  onMaximize?: () => void;
  onResize?: (width: number, height: number) => void;
  onMove?: (x: number, y: number) => void;
}

export const DesktopWindow: React.FC<DesktopWindowProps> = ({
  title,
  children,
  width = 800,
  height = 600,
  x = 100,
  y = 100,
  resizable = true,
  minimizable = true,
  maximizable = true,
  closable = true,
  onClose,
  onMinimize,
  onMaximize,
  onResize,
  onMove
}) => {
  const [position, setPosition] = useState({ x, y });
  const [size, setSize] = useState({ width, height });
  const [isMaximized, setIsMaximized] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const windowRef = useRef<HTMLDivElement>(null);
  const dragStartRef = useRef({ x: 0, y: 0 });

  // 拖拽窗口
  const handleMouseDown = (e: React.MouseEvent) => {
    if (isMaximized) return;
    
    setIsDragging(true);
    dragStartRef.current = {
      x: e.clientX - position.x,
      y: e.clientY - position.y
    };
  };

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (isDragging && !isMaximized) {
      const newX = e.clientX - dragStartRef.current.x;
      const newY = e.clientY - dragStartRef.current.y;
      setPosition({ x: newX, y: newY });
      onMove?.(newX, newY);
    }
  }, [isDragging, isMaximized, onMove]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    setIsResizing(false);
  }, []);

  useEffect(() => {
    if (isDragging || isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, isResizing, handleMouseMove, handleMouseUp]);

  // 最大化/还原
  const handleMaximize = () => {
    setIsMaximized(!isMaximized);
    onMaximize?.();
  };

  // 最小化
  const handleMinimize = () => {
    onMinimize?.();
  };

  // 关闭
  const handleClose = () => {
    onClose?.();
  };

  return (
    <div
      ref={windowRef}
      className={`desktop-window ${isMaximized ? 'maximized' : ''}`}
      style={{
        left: isMaximized ? 0 : position.x,
        top: isMaximized ? 0 : position.y,
        width: isMaximized ? '100vw' : size.width,
        height: isMaximized ? '100vh' : size.height
      }}
    >
      {/* 窗口标题栏 */}
      <div 
        className="window-titlebar"
        onMouseDown={handleMouseDown}
        onDoubleClick={maximizable ? handleMaximize : undefined}
      >
        <div className="window-title">{title}</div>
        <div className="window-controls">
          {minimizable && (
            <button className="window-control minimize" onClick={handleMinimize}>
              <MinusOutlined />
            </button>
          )}
          {maximizable && (
            <button className="window-control maximize" onClick={handleMaximize}>
              <BorderOutlined />
            </button>
          )}
          {closable && (
            <button className="window-control close" onClick={handleClose}>
              <CloseOutlined />
            </button>
          )}
        </div>
      </div>

      {/* 窗口内容 */}
      <div className="window-content">
        {children}
      </div>

      {/* 调整大小手柄 */}
      {resizable && !isMaximized && (
        <>
          <div className="resize-handle resize-right" />
          <div className="resize-handle resize-bottom" />
          <div className="resize-handle resize-corner" />
        </>
      )}
    </div>
  );
};

/**
 * 桌面端菜单栏
 */
export interface DesktopMenuBarProps {
  menus: Array<{
    key: string;
    label: string;
    items: Array<{
      key: string;
      label: string;
      icon?: React.ReactNode;
      shortcut?: string;
      disabled?: boolean;
      onClick?: () => void;
      children?: Array<{
        key: string;
        label: string;
        icon?: React.ReactNode;
        onClick?: () => void;
      }>;
    }>;
  }>;
}

export const DesktopMenuBar: React.FC<DesktopMenuBarProps> = ({ menus }) => {
  const [activeMenu, setActiveMenu] = useState<string | null>(null);

  const renderMenuItem = (item: any) => (
    <Menu.Item 
      key={item.key} 
      icon={item.icon}
      disabled={item.disabled}
      onClick={item.onClick}
    >
      <div className="menu-item-content">
        <span>{item.label}</span>
        {item.shortcut && (
          <span className="menu-shortcut">{item.shortcut}</span>
        )}
      </div>
    </Menu.Item>
  );

  const renderSubMenu = (item: any) => (
    <Menu.SubMenu key={item.key} icon={item.icon} title={item.label}>
      {item.children?.map((child: any) => renderMenuItem(child))}
    </Menu.SubMenu>
  );

  return (
    <div className="desktop-menubar">
      {menus.map(menu => (
        <Dropdown
          key={menu.key}
          overlay={
            <Menu>
              {menu.items.map(item => 
                item.children ? renderSubMenu(item) : renderMenuItem(item)
              )}
            </Menu>
          }
          trigger={['click']}
          onOpenChange={(open) => setActiveMenu(open ? menu.key : null)}
        >
          <div 
            className={`menu-item ${activeMenu === menu.key ? 'active' : ''}`}
          >
            {menu.label}
          </div>
        </Dropdown>
      ))}
    </div>
  );
};

/**
 * 桌面端工具栏
 */
export interface DesktopToolBarProps {
  tools: Array<{
    key: string;
    icon: React.ReactNode;
    label: string;
    tooltip?: string;
    disabled?: boolean;
    active?: boolean;
    onClick?: () => void;
  }>;
  orientation?: 'horizontal' | 'vertical';
}

export const DesktopToolBar: React.FC<DesktopToolBarProps> = ({
  tools,
  orientation = 'horizontal'
}) => {
  return (
    <div className={`desktop-toolbar ${orientation}`}>
      {tools.map(tool => (
        <Tooltip key={tool.key} title={tool.tooltip || tool.label}>
          <button
            className={`toolbar-button ${tool.active ? 'active' : ''}`}
            disabled={tool.disabled}
            onClick={tool.onClick}
          >
            {tool.icon}
          </button>
        </Tooltip>
      ))}
    </div>
  );
};

/**
 * 桌面端侧边栏
 */
export interface DesktopSideBarProps {
  width?: number;
  position?: 'left' | 'right';
  collapsible?: boolean;
  collapsed?: boolean;
  onCollapse?: (collapsed: boolean) => void;
  children: React.ReactNode;
}

export const DesktopSideBar: React.FC<DesktopSideBarProps> = ({
  width = 250,
  position = 'left',
  collapsible = true,
  collapsed = false,
  onCollapse,
  children
}) => {
  const [isCollapsed, setIsCollapsed] = useState(collapsed);

  const handleCollapse = () => {
    const newCollapsed = !isCollapsed;
    setIsCollapsed(newCollapsed);
    onCollapse?.(newCollapsed);
  };

  return (
    <div 
      className={`desktop-sidebar ${position} ${isCollapsed ? 'collapsed' : ''}`}
      style={{ width: isCollapsed ? 48 : width }}
    >
      {collapsible && (
        <button className="sidebar-collapse-button" onClick={handleCollapse}>
          <MenuOutlined />
        </button>
      )}
      <div className="sidebar-content">
        {children}
      </div>
    </div>
  );
};

/**
 * 桌面端状态栏
 */
export interface DesktopStatusBarProps {
  leftItems?: React.ReactNode[];
  rightItems?: React.ReactNode[];
  centerItems?: React.ReactNode[];
}

export const DesktopStatusBar: React.FC<DesktopStatusBarProps> = ({
  leftItems = [],
  rightItems = [],
  centerItems = []
}) => {
  return (
    <div className="desktop-statusbar">
      <div className="statusbar-section left">
        {leftItems.map((item, index) => (
          <div key={index} className="statusbar-item">
            {item}
          </div>
        ))}
      </div>
      
      <div className="statusbar-section center">
        {centerItems.map((item, index) => (
          <div key={index} className="statusbar-item">
            {item}
          </div>
        ))}
      </div>
      
      <div className="statusbar-section right">
        {rightItems.map((item, index) => (
          <div key={index} className="statusbar-item">
            {item}
          </div>
        ))}
      </div>
    </div>
  );
};

/**
 * 桌面端文件浏览器
 */
export interface DesktopFileExplorerProps {
  files: Array<{
    key: string;
    name: string;
    type: 'file' | 'folder';
    size?: number;
    modified?: Date;
    children?: any[];
  }>;
  onFileSelect?: (file: any) => void;
  onFileDoubleClick?: (file: any) => void;
  onFolderExpand?: (folder: any) => void;
}

export const DesktopFileExplorer: React.FC<DesktopFileExplorerProps> = ({
  files,
  onFileSelect,
  onFileDoubleClick,
  onFolderExpand
}) => {
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);

  const renderTreeNode = (file: any) => ({
    title: (
      <div className="file-node">
        {file.type === 'folder' ? <FolderOutlined /> : <FileOutlined />}
        <span className="file-name">{file.name}</span>
        {file.size && (
          <span className="file-size">{formatFileSize(file.size)}</span>
        )}
      </div>
    ),
    key: file.key,
    children: file.children?.map(renderTreeNode)
  });

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleSelect = (selectedKeys: React.Key[], info: any) => {
    setSelectedKeys(selectedKeys as string[]);
    if (selectedKeys.length > 0) {
      const file = findFileByKey(files, selectedKeys[0] as string);
      onFileSelect?.(file);
    }
  };

  const handleDoubleClick = (e: React.MouseEvent, node: any) => {
    const file = findFileByKey(files, node.key);
    onFileDoubleClick?.(file);
  };

  const handleExpand = (expandedKeys: React.Key[]) => {
    setExpandedKeys(expandedKeys as string[]);
  };

  const findFileByKey = (files: any[], key: string): any => {
    for (const file of files) {
      if (file.key === key) return file;
      if (file.children) {
        const found = findFileByKey(file.children, key);
        if (found) return found;
      }
    }
    return null;
  };

  return (
    <div className="desktop-file-explorer">
      <Tree
        treeData={files.map(renderTreeNode)}
        selectedKeys={selectedKeys}
        expandedKeys={expandedKeys}
        onSelect={handleSelect}
        onExpand={handleExpand}
        onDoubleClick={handleDoubleClick}
        showIcon={false}
      />
    </div>
  );
};

/**
 * 桌面端多标签页
 */
export interface DesktopTabsProps {
  tabs: Array<{
    key: string;
    title: string;
    content: React.ReactNode;
    closable?: boolean;
    modified?: boolean;
  }>;
  activeKey?: string;
  onTabChange?: (key: string) => void;
  onTabClose?: (key: string) => void;
  onTabAdd?: () => void;
}

export const DesktopTabs: React.FC<DesktopTabsProps> = ({
  tabs,
  activeKey,
  onTabChange,
  onTabClose,
  onTabAdd
}) => {
  const renderTabTitle = (tab: any) => (
    <div className="tab-title">
      <span className={tab.modified ? 'modified' : ''}>{tab.title}</span>
      {tab.closable !== false && (
        <CloseOutlined 
          className="tab-close"
          onClick={(e) => {
            e.stopPropagation();
            onTabClose?.(tab.key);
          }}
        />
      )}
    </div>
  );

  return (
    <div className="desktop-tabs">
      <Tabs
        type="editable-card"
        activeKey={activeKey}
        onChange={onTabChange}
        onEdit={(targetKey, action) => {
          if (action === 'add') {
            onTabAdd?.();
          } else if (action === 'remove') {
            onTabClose?.(targetKey as string);
          }
        }}
        hideAdd={!onTabAdd}
      >
        {tabs.map(tab => (
          <Tabs.TabPane
            key={tab.key}
            tab={renderTabTitle(tab)}
            closable={tab.closable !== false}
          >
            {tab.content}
          </Tabs.TabPane>
        ))}
      </Tabs>
    </div>
  );
};
