/**
 * OptimizedDragSystem.tsx
 * 
 * 优化的拖拽系统，提供更好的性能和用户体验
 */

import React, { useState, useRef, useCallback, useEffect, useMemo } from 'react';
import { throttle, debounce } from 'lodash';

/**
 * 拖拽配置接口
 */
export interface DragConfig {
  /** 拖拽阈值（像素） */
  threshold: number;
  /** 更新频率（毫秒） */
  updateInterval: number;
  /** 是否启用惯性滚动 */
  enableInertia: boolean;
  /** 惯性衰减系数 */
  inertiaDecay: number;
  /** 是否启用磁性吸附 */
  enableSnapping: boolean;
  /** 吸附距离（像素） */
  snapDistance: number;
  /** 是否启用边界检测 */
  enableBoundaryCheck: boolean;
  /** 是否启用碰撞检测 */
  enableCollisionDetection: boolean;
}

/**
 * 拖拽状态接口
 */
export interface DragState {
  isDragging: boolean;
  startPosition: { x: number; y: number };
  currentPosition: { x: number; y: number };
  deltaPosition: { x: number; y: number };
  velocity: { x: number; y: number };
  element: HTMLElement | null;
  data: any;
}

/**
 * 拖拽事件接口
 */
export interface DragEvents {
  onDragStart?: (state: DragState) => void;
  onDragMove?: (state: DragState) => void;
  onDragEnd?: (state: DragState) => void;
  onDragCancel?: (state: DragState) => void;
  onDrop?: (state: DragState, target: HTMLElement) => void;
}

/**
 * 默认配置
 */
const DEFAULT_CONFIG: DragConfig = {
  threshold: 5,
  updateInterval: 16, // 60fps
  enableInertia: true,
  inertiaDecay: 0.95,
  enableSnapping: true,
  snapDistance: 10,
  enableBoundaryCheck: true,
  enableCollisionDetection: true
};

/**
 * 优化的拖拽Hook
 */
export const useOptimizedDrag = (
  config: Partial<DragConfig> = {},
  events: DragEvents = {}
) => {
  const finalConfig = useMemo(() => ({ ...DEFAULT_CONFIG, ...config }), [config]);
  
  const [dragState, setDragState] = useState<DragState>({
    isDragging: false,
    startPosition: { x: 0, y: 0 },
    currentPosition: { x: 0, y: 0 },
    deltaPosition: { x: 0, y: 0 },
    velocity: { x: 0, y: 0 },
    element: null,
    data: null
  });

  const dragRef = useRef<HTMLElement>(null);
  const animationFrameRef = useRef<number>();
  const lastUpdateTime = useRef<number>(0);
  const velocityHistory = useRef<Array<{ x: number; y: number; time: number }>>([]);
  const isDragStarted = useRef<boolean>(false);

  // 节流的拖拽更新函数
  const throttledDragMove = useCallback(
    throttle((newState: DragState) => {
      setDragState(newState);
      events.onDragMove?.(newState);
    }, finalConfig.updateInterval),
    [events.onDragMove, finalConfig.updateInterval]
  );

  // 计算速度
  const calculateVelocity = useCallback((currentPos: { x: number; y: number }, currentTime: number) => {
    const history = velocityHistory.current;
    history.push({ ...currentPos, time: currentTime });
    
    // 只保留最近100ms的历史
    const cutoffTime = currentTime - 100;
    while (history.length > 0 && history[0].time < cutoffTime) {
      history.shift();
    }
    
    if (history.length < 2) {
      return { x: 0, y: 0 };
    }
    
    const first = history[0];
    const last = history[history.length - 1];
    const timeDiff = last.time - first.time;
    
    if (timeDiff === 0) {
      return { x: 0, y: 0 };
    }
    
    return {
      x: (last.x - first.x) / timeDiff,
      y: (last.y - first.y) / timeDiff
    };
  }, []);

  // 磁性吸附
  const applySnapping = useCallback((position: { x: number; y: number }) => {
    if (!finalConfig.enableSnapping) {
      return position;
    }

    // 这里可以实现网格吸附、元素边缘吸附等逻辑
    const snapDistance = finalConfig.snapDistance;
    const gridSize = 10; // 网格大小
    
    const snappedX = Math.round(position.x / gridSize) * gridSize;
    const snappedY = Math.round(position.y / gridSize) * gridSize;
    
    const deltaX = Math.abs(position.x - snappedX);
    const deltaY = Math.abs(position.y - snappedY);
    
    return {
      x: deltaX <= snapDistance ? snappedX : position.x,
      y: deltaY <= snapDistance ? snappedY : position.y
    };
  }, [finalConfig.enableSnapping, finalConfig.snapDistance]);

  // 边界检测
  const applyBoundaryCheck = useCallback((position: { x: number; y: number }, element: HTMLElement) => {
    if (!finalConfig.enableBoundaryCheck || !element.parentElement) {
      return position;
    }

    const parent = element.parentElement;
    const parentRect = parent.getBoundingClientRect();
    const elementRect = element.getBoundingClientRect();
    
    const minX = 0;
    const minY = 0;
    const maxX = parentRect.width - elementRect.width;
    const maxY = parentRect.height - elementRect.height;
    
    return {
      x: Math.max(minX, Math.min(maxX, position.x)),
      y: Math.max(minY, Math.min(maxY, position.y))
    };
  }, [finalConfig.enableBoundaryCheck]);

  // 处理鼠标按下
  const handleMouseDown = useCallback((e: MouseEvent) => {
    if (!dragRef.current) return;

    e.preventDefault();
    isDragStarted.current = false;
    velocityHistory.current = [];

    const startPos = { x: e.clientX, y: e.clientY };
    
    const initialState: DragState = {
      isDragging: false,
      startPosition: startPos,
      currentPosition: startPos,
      deltaPosition: { x: 0, y: 0 },
      velocity: { x: 0, y: 0 },
      element: dragRef.current,
      data: dragRef.current.dataset.dragData ? JSON.parse(dragRef.current.dataset.dragData) : null
    };

    setDragState(initialState);

    // 添加全局事件监听器
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    document.addEventListener('selectstart', preventDefault);
  }, []);

  // 处理鼠标移动
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!dragRef.current) return;

    const currentPos = { x: e.clientX, y: e.clientY };
    const currentTime = performance.now();
    
    setDragState(prevState => {
      const deltaX = currentPos.x - prevState.startPosition.x;
      const deltaY = currentPos.y - prevState.startPosition.y;
      const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
      
      // 检查是否超过拖拽阈值
      if (!isDragStarted.current && distance > finalConfig.threshold) {
        isDragStarted.current = true;
        events.onDragStart?.(prevState);
      }
      
      if (!isDragStarted.current) {
        return prevState;
      }

      // 计算速度
      const velocity = calculateVelocity(currentPos, currentTime);
      
      // 应用吸附和边界检测
      let adjustedPos = applySnapping(currentPos);
      adjustedPos = applyBoundaryCheck(adjustedPos, dragRef.current!);
      
      const newState: DragState = {
        ...prevState,
        isDragging: true,
        currentPosition: adjustedPos,
        deltaPosition: {
          x: adjustedPos.x - prevState.startPosition.x,
          y: adjustedPos.y - prevState.startPosition.y
        },
        velocity
      };

      // 使用节流更新
      if (currentTime - lastUpdateTime.current >= finalConfig.updateInterval) {
        throttledDragMove(newState);
        lastUpdateTime.current = currentTime;
      }

      return newState;
    });
  }, [finalConfig.threshold, finalConfig.updateInterval, calculateVelocity, applySnapping, applyBoundaryCheck, throttledDragMove, events.onDragStart]);

  // 处理鼠标释放
  const handleMouseUp = useCallback((e: MouseEvent) => {
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
    document.removeEventListener('selectstart', preventDefault);

    if (isDragStarted.current) {
      setDragState(prevState => {
        const finalState = {
          ...prevState,
          isDragging: false
        };

        // 检查是否有有效的放置目标
        const elementUnderMouse = document.elementFromPoint(e.clientX, e.clientY) as HTMLElement;
        if (elementUnderMouse && elementUnderMouse.dataset.dropTarget) {
          events.onDrop?.(finalState, elementUnderMouse);
        }

        events.onDragEnd?.(finalState);
        return finalState;
      });
    }

    isDragStarted.current = false;
    velocityHistory.current = [];
  }, [handleMouseMove, events.onDragEnd, events.onDrop]);

  // 阻止默认选择行为
  const preventDefault = useCallback((e: Event) => {
    e.preventDefault();
  }, []);

  // 设置拖拽元素引用
  const setDragRef = useCallback((element: HTMLElement | null) => {
    if (dragRef.current) {
      dragRef.current.removeEventListener('mousedown', handleMouseDown);
    }

    dragRef.current = element;

    if (element) {
      element.addEventListener('mousedown', handleMouseDown);
      element.style.userSelect = 'none';
      element.style.cursor = 'grab';
    }
  }, [handleMouseDown]);

  // 清理事件监听器
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('selectstart', preventDefault);
      
      if (dragRef.current) {
        dragRef.current.removeEventListener('mousedown', handleMouseDown);
      }
    };
  }, [handleMouseMove, handleMouseUp, preventDefault, handleMouseDown]);

  return {
    dragState,
    setDragRef,
    isDragging: dragState.isDragging
  };
};

/**
 * 优化的拖拽组件
 */
export interface OptimizedDragProps {
  children: React.ReactNode;
  data?: any;
  config?: Partial<DragConfig>;
  events?: DragEvents;
  className?: string;
  style?: React.CSSProperties;
}

export const OptimizedDrag: React.FC<OptimizedDragProps> = ({
  children,
  data,
  config,
  events,
  className,
  style
}) => {
  const { dragState, setDragRef, isDragging } = useOptimizedDrag(config, events);

  const dragStyle: React.CSSProperties = {
    ...style,
    opacity: isDragging ? 0.8 : 1,
    transform: isDragging ? 'scale(1.05)' : 'scale(1)',
    transition: isDragging ? 'none' : 'all 0.2s ease',
    zIndex: isDragging ? 1000 : 'auto',
    cursor: isDragging ? 'grabbing' : 'grab'
  };

  return (
    <div
      ref={setDragRef}
      className={`optimized-drag ${className || ''} ${isDragging ? 'dragging' : ''}`}
      style={dragStyle}
      data-drag-data={data ? JSON.stringify(data) : undefined}
    >
      {children}
    </div>
  );
};

/**
 * 优化的放置区域Hook
 */
export const useOptimizedDrop = (
  onDrop: (data: any, position: { x: number; y: number }) => void,
  config: { accept?: string[] } = {}
) => {
  const dropRef = useRef<HTMLElement>(null);
  const [isOver, setIsOver] = useState(false);

  const setDropRef = useCallback((element: HTMLElement | null) => {
    dropRef.current = element;
    
    if (element) {
      element.dataset.dropTarget = 'true';
      
      element.addEventListener('dragover', (e) => {
        e.preventDefault();
        setIsOver(true);
      });
      
      element.addEventListener('dragleave', () => {
        setIsOver(false);
      });
      
      element.addEventListener('drop', (e) => {
        e.preventDefault();
        setIsOver(false);
        
        const data = e.dataTransfer?.getData('application/json');
        if (data) {
          const parsedData = JSON.parse(data);
          const rect = element.getBoundingClientRect();
          const position = {
            x: e.clientX - rect.left,
            y: e.clientY - rect.top
          };
          onDrop(parsedData, position);
        }
      });
    }
  }, [onDrop]);

  return {
    setDropRef,
    isOver
  };
};

export default OptimizedDrag;
