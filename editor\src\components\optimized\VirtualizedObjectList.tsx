/**
 * 虚拟化对象列表组件
 * 专为支持100+并发用户优化的高性能列表组件
 */
import React, { useMemo, useCallback, useState, useEffect, useRef } from 'react';
import { FixedSizeList as List, ListChildComponentProps } from 'react-window';
import { Tree, Input, Button, Tooltip, Dropdown, Menu } from 'antd';
import {
  EyeOutlined,
  EyeInvisibleOutlined,
  LockOutlined,
  UnlockOutlined,
  MoreOutlined,
  SearchOutlined,
  FolderOutlined,
  FileOutlined,
} from '@ant-design/icons';
import { useAppSelector, useAppDispatch } from '../../hooks/redux';
import {
  selectAllObjects,
  selectSelectedObjectIds,
  selectLockedObjects,
  setSelectedObjects,
  addToSelection,
  removeFromSelection,
  updateObject,
  lockObject,
  unlockObject,
} from '../../store/optimized/OptimizedEditorSlice';
import './VirtualizedObjectList.css';

const { Search } = Input;

// 对象项接口
interface ObjectItem {
  id: string;
  name: string;
  type: string;
  parentId?: string;
  visible: boolean;
  locked: boolean;
  children?: ObjectItem[];
  level: number;
  expanded: boolean;
}

// 列表项组件属性
interface ObjectListItemProps extends ListChildComponentProps {
  data: {
    items: ObjectItem[];
    selectedIds: string[];
    lockedObjectIds: string[];
    onSelect: (id: string, multiSelect: boolean) => void;
    onToggleVisibility: (id: string) => void;
    onToggleLock: (id: string) => void;
    onToggleExpand: (id: string) => void;
    onContextMenu: (id: string, event: React.MouseEvent) => void;
  };
}

// 单个列表项组件
const ObjectListItem: React.FC<ObjectListItemProps> = ({ index, style, data }) => {
  const { items, selectedIds, lockedObjectIds, onSelect, onToggleVisibility, onToggleLock, onToggleExpand, onContextMenu } = data;
  const item = items[index];
  
  if (!item) return null;
  
  const isSelected = selectedIds.includes(item.id);
  const isLocked = lockedObjectIds.includes(item.id);
  const hasChildren = item.children && item.children.length > 0;
  
  const handleClick = useCallback((event: React.MouseEvent) => {
    event.stopPropagation();
    onSelect(item.id, event.ctrlKey || event.metaKey);
  }, [item.id, onSelect]);
  
  const handleVisibilityToggle = useCallback((event: React.MouseEvent) => {
    event.stopPropagation();
    onToggleVisibility(item.id);
  }, [item.id, onToggleVisibility]);
  
  const handleLockToggle = useCallback((event: React.MouseEvent) => {
    event.stopPropagation();
    onToggleLock(item.id);
  }, [item.id, onToggleLock]);
  
  const handleExpandToggle = useCallback((event: React.MouseEvent) => {
    event.stopPropagation();
    if (hasChildren) {
      onToggleExpand(item.id);
    }
  }, [item.id, hasChildren, onToggleExpand]);
  
  const handleContextMenu = useCallback((event: React.MouseEvent) => {
    event.preventDefault();
    onContextMenu(item.id, event);
  }, [item.id, onContextMenu]);
  
  return (
    <div
      style={style}
      className={`object-list-item ${isSelected ? 'selected' : ''} ${isLocked ? 'locked' : ''}`}
      onClick={handleClick}
      onContextMenu={handleContextMenu}
    >
      <div
        className="item-content"
        style={{ paddingLeft: `${item.level * 20 + 8}px` }}
      >
        {/* 展开/折叠按钮 */}
        <div className="expand-button" onClick={handleExpandToggle}>
          {hasChildren ? (
            <span className={`expand-icon ${item.expanded ? 'expanded' : ''}`}>▶</span>
          ) : (
            <span className="expand-placeholder"></span>
          )}
        </div>
        
        {/* 对象图标 */}
        <div className="object-icon">
          {hasChildren ? <FolderOutlined /> : <FileOutlined />}
        </div>
        
        {/* 对象名称 */}
        <div className="object-name" title={item.name}>
          {item.name}
        </div>
        
        {/* 操作按钮 */}
        <div className="object-actions">
          <Tooltip title={item.visible ? '隐藏' : '显示'}>
            <Button
              type="text"
              size="small"
              icon={item.visible ? <EyeOutlined /> : <EyeInvisibleOutlined />}
              onClick={handleVisibilityToggle}
              className={`action-button ${!item.visible ? 'inactive' : ''}`}
            />
          </Tooltip>
          
          <Tooltip title={isLocked ? '解锁' : '锁定'}>
            <Button
              type="text"
              size="small"
              icon={isLocked ? <LockOutlined /> : <UnlockOutlined />}
              onClick={handleLockToggle}
              className={`action-button ${isLocked ? 'active' : ''}`}
            />
          </Tooltip>
        </div>
      </div>
    </div>
  );
};

// 虚拟化对象列表组件
export const VirtualizedObjectList: React.FC = () => {
  const dispatch = useAppDispatch();
  const allObjects = useAppSelector(selectAllObjects);
  const selectedIds = useAppSelector(selectSelectedObjectIds);
  const lockedObjects = useAppSelector(selectLockedObjects);
  
  // 本地状态
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedIds, setExpandedIds] = useState<Set<string>>(new Set());
  const [contextMenu, setContextMenu] = useState<{
    visible: boolean;
    x: number;
    y: number;
    objectId: string;
  }>({ visible: false, x: 0, y: 0, objectId: '' });
  
  const listRef = useRef<List>(null);
  
  // 锁定对象ID列表
  const lockedObjectIds = useMemo(() => {
    return lockedObjects.map(obj => obj.id);
  }, [lockedObjects]);
  
  // 构建层次化对象树
  const objectTree = useMemo(() => {
    const objects = Object.values(allObjects);
    const rootObjects = objects.filter(obj => !obj.parentId);
    
    const buildTree = (parentObjects: any[], level = 0): ObjectItem[] => {
      return parentObjects.map(obj => {
        const children = objects.filter(child => child.parentId === obj.id);
        const hasChildren = children.length > 0;
        const isExpanded = expandedIds.has(obj.id);
        
        const item: ObjectItem = {
          id: obj.id,
          name: obj.name,
          type: obj.type,
          parentId: obj.parentId,
          visible: obj.visible,
          locked: lockedObjectIds.includes(obj.id),
          level,
          expanded: isExpanded,
          children: hasChildren ? buildTree(children, level + 1) : undefined,
        };
        
        return item;
      });
    };
    
    return buildTree(rootObjects);
  }, [allObjects, expandedIds, lockedObjectIds]);
  
  // 扁平化树结构用于虚拟化列表
  const flattenedItems = useMemo(() => {
    const flatten = (items: ObjectItem[]): ObjectItem[] => {
      const result: ObjectItem[] = [];
      
      for (const item of items) {
        // 应用搜索过滤
        if (searchTerm && !item.name.toLowerCase().includes(searchTerm.toLowerCase())) {
          continue;
        }
        
        result.push(item);
        
        // 如果展开且有子项，递归添加子项
        if (item.expanded && item.children) {
          result.push(...flatten(item.children));
        }
      }
      
      return result;
    };
    
    return flatten(objectTree);
  }, [objectTree, searchTerm]);
  
  // 处理对象选择
  const handleSelect = useCallback((id: string, multiSelect: boolean) => {
    if (multiSelect) {
      if (selectedIds.includes(id)) {
        dispatch(removeFromSelection(id));
      } else {
        dispatch(addToSelection(id));
      }
    } else {
      dispatch(setSelectedObjects([id]));
    }
  }, [dispatch, selectedIds]);
  
  // 处理可见性切换
  const handleToggleVisibility = useCallback((id: string) => {
    const object = allObjects[id];
    if (object) {
      dispatch(updateObject({
        id,
        changes: { visible: !object.visible }
      }));
    }
  }, [dispatch, allObjects]);
  
  // 处理锁定切换
  const handleToggleLock = useCallback((id: string) => {
    const isLocked = lockedObjectIds.includes(id);
    if (isLocked) {
      dispatch(unlockObject(id));
    } else {
      dispatch(lockObject({ objectId: id, userId: 'current-user' })); // 应该使用实际用户ID
    }
  }, [dispatch, lockedObjectIds]);
  
  // 处理展开/折叠
  const handleToggleExpand = useCallback((id: string) => {
    setExpandedIds(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  }, []);
  
  // 处理右键菜单
  const handleContextMenu = useCallback((id: string, event: React.MouseEvent) => {
    setContextMenu({
      visible: true,
      x: event.clientX,
      y: event.clientY,
      objectId: id,
    });
  }, []);
  
  // 关闭右键菜单
  const handleCloseContextMenu = useCallback(() => {
    setContextMenu(prev => ({ ...prev, visible: false }));
  }, []);
  
  // 右键菜单项
  const contextMenuItems = useMemo(() => {
    const object = allObjects[contextMenu.objectId];
    if (!object) return [];
    
    return [
      {
        key: 'duplicate',
        label: '复制',
        onClick: () => {
          // 实现复制逻辑
          console.log('复制对象:', contextMenu.objectId);
          handleCloseContextMenu();
        },
      },
      {
        key: 'delete',
        label: '删除',
        danger: true,
        onClick: () => {
          // 实现删除逻辑
          console.log('删除对象:', contextMenu.objectId);
          handleCloseContextMenu();
        },
      },
      {
        type: 'divider',
      },
      {
        key: 'rename',
        label: '重命名',
        onClick: () => {
          // 实现重命名逻辑
          console.log('重命名对象:', contextMenu.objectId);
          handleCloseContextMenu();
        },
      },
    ];
  }, [allObjects, contextMenu.objectId, handleCloseContextMenu]);
  
  // 搜索处理
  const handleSearch = useCallback((value: string) => {
    setSearchTerm(value);
  }, []);
  
  // 清空搜索
  const handleClearSearch = useCallback(() => {
    setSearchTerm('');
  }, []);
  
  // 全部展开/折叠
  const handleExpandAll = useCallback(() => {
    const allIds = new Set(Object.keys(allObjects));
    setExpandedIds(allIds);
  }, [allObjects]);
  
  const handleCollapseAll = useCallback(() => {
    setExpandedIds(new Set());
  }, []);
  
  // 列表数据
  const listData = useMemo(() => ({
    items: flattenedItems,
    selectedIds,
    lockedObjectIds,
    onSelect: handleSelect,
    onToggleVisibility: handleToggleVisibility,
    onToggleLock: handleToggleLock,
    onToggleExpand: handleToggleExpand,
    onContextMenu: handleContextMenu,
  }), [
    flattenedItems,
    selectedIds,
    lockedObjectIds,
    handleSelect,
    handleToggleVisibility,
    handleToggleLock,
    handleToggleExpand,
    handleContextMenu,
  ]);
  
  // 监听点击外部关闭右键菜单
  useEffect(() => {
    const handleClickOutside = () => {
      if (contextMenu.visible) {
        handleCloseContextMenu();
      }
    };
    
    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, [contextMenu.visible, handleCloseContextMenu]);
  
  return (
    <div className="virtualized-object-list">
      {/* 工具栏 */}
      <div className="list-toolbar">
        <Search
          placeholder="搜索对象..."
          value={searchTerm}
          onChange={(e) => handleSearch(e.target.value)}
          onSearch={handleSearch}
          allowClear
          style={{ flex: 1 }}
        />
        
        <div className="toolbar-actions">
          <Tooltip title="全部展开">
            <Button type="text" size="small" onClick={handleExpandAll}>
              展开
            </Button>
          </Tooltip>
          <Tooltip title="全部折叠">
            <Button type="text" size="small" onClick={handleCollapseAll}>
              折叠
            </Button>
          </Tooltip>
        </div>
      </div>
      
      {/* 虚拟化列表 */}
      <div className="list-container">
        <List
          ref={listRef}
          height={400} // 可以根据容器大小动态调整
          itemCount={flattenedItems.length}
          itemSize={32} // 每项高度
          itemData={listData}
          overscanCount={5} // 预渲染项数
        >
          {ObjectListItem}
        </List>
      </div>
      
      {/* 右键菜单 */}
      {contextMenu.visible && (
        <div
          className="context-menu-overlay"
          style={{
            position: 'fixed',
            top: contextMenu.y,
            left: contextMenu.x,
            zIndex: 1000,
          }}
        >
          <Menu
            items={contextMenuItems}
            onClick={({ key }) => {
              const item = contextMenuItems.find(item => item.key === key);
              if (item && 'onClick' in item) {
                item.onClick();
              }
            }}
          />
        </div>
      )}
      
      {/* 空状态 */}
      {flattenedItems.length === 0 && (
        <div className="empty-state">
          {searchTerm ? '未找到匹配的对象' : '场景中没有对象'}
        </div>
      )}
    </div>
  );
};

export default VirtualizedObjectList;
