/**
 * RAG对话测试面板
 */
import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Input,
  Button,
  Space,
  Avatar,
  Typography,
  Divider,
  Select,
  Switch,
  Slider,
  Row,
  Col,
  Tag,
  Tooltip,
  message,
  Spin,
  Empty,
} from 'antd';
import {
  SendOutlined,
  SoundOutlined,
  MutedOutlined,
  RobotOutlined,
  UserOutlined,
  SettingOutlined,
  ClearOutlined,
  DownloadOutlined,
  PlayCircleOutlined,
  StopOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Text, Title } = Typography;
const { TextArea } = Input;
const { Option } = Select;

/**
 * 消息接口
 */
interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: number;
  audioData?: string; // base64编码的音频数据
  emotion?: {
    type: string;
    intensity: number;
  };
  sources?: Array<{
    id: string;
    content: string;
    score: number;
  }>;
  confidence?: number;
  processingTime?: number;
}

/**
 * 对话配置接口
 */
interface DialogueConfig {
  knowledgeBaseId: string;
  avatarId: string;
  enableVoiceInput: boolean;
  enableVoiceOutput: boolean;
  voiceSettings: {
    inputLanguage: string;
    outputVoice: string;
    rate: number;
    pitch: number;
    volume: number;
  };
  ragSettings: {
    searchThreshold: number;
    maxResults: number;
    contextLength: number;
  };
}

/**
 * RAG对话测试面板组件
 */
const RAGDialoguePanel: React.FC = () => {
  const { t } = useTranslation();
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [config, setConfig] = useState<DialogueConfig>({
    knowledgeBaseId: 'kb_1',
    avatarId: 'avatar_1',
    enableVoiceInput: true,
    enableVoiceOutput: true,
    voiceSettings: {
      inputLanguage: 'zh-CN',
      outputVoice: 'zh-CN-XiaoxiaoNeural',
      rate: 1.0,
      pitch: 1.0,
      volume: 1.0,
    },
    ragSettings: {
      searchThreshold: 0.7,
      maxResults: 5,
      contextLength: 10,
    },
  });
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const audioRef = useRef<HTMLAudioElement>(null);

  /**
   * 滚动到底部
   */
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  /**
   * 发送消息
   */
  const handleSendMessage = async (content: string, type: 'text' | 'voice' = 'text') => {
    if (!content.trim()) return;

    const userMessage: Message = {
      id: `msg_${Date.now()}_user`,
      role: 'user',
      content,
      timestamp: Date.now(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsLoading(true);

    try {
      // 这里调用RAG对话API
      const response = await mockRAGDialogue(content, config);
      
      const assistantMessage: Message = {
        id: `msg_${Date.now()}_assistant`,
        role: 'assistant',
        content: response.text,
        timestamp: Date.now(),
        emotion: response.emotion,
        sources: response.sources,
        confidence: response.confidence,
        processingTime: response.processingTime,
        audioData: response.audioData,
      };

      setMessages(prev => [...prev, assistantMessage]);

      // 如果启用语音输出，自动播放
      if (config.enableVoiceOutput && response.audioData) {
        playAudio(response.audioData);
      }

    } catch (error) {
      message.error('发送消息失败');
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 模拟RAG对话API
   */
  const mockRAGDialogue = async (text: string, config: DialogueConfig) => {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

    return {
      text: `这是对"${text}"的智能回答。基于知识库的检索结果，我为您提供以下信息...`,
      emotion: {
        type: 'friendly',
        intensity: 0.8,
      },
      sources: [
        {
          id: 'doc_1',
          content: '相关文档片段1...',
          score: 0.95,
        },
        {
          id: 'doc_2',
          content: '相关文档片段2...',
          score: 0.87,
        },
      ],
      confidence: 0.92,
      processingTime: 1500,
      audioData: config.enableVoiceOutput ? 'mock_audio_data' : undefined,
    };
  };

  /**
   * 开始语音录制
   */
  const handleStartRecording = async () => {
    if (!config.enableVoiceInput) {
      message.warning('请先启用语音输入');
      return;
    }

    try {
      setIsRecording(true);
      // 这里实现语音录制逻辑
      console.log('开始语音录制');
      
      // 模拟录制过程
      setTimeout(() => {
        setIsRecording(false);
        const mockTranscript = '这是语音识别的结果';
        handleSendMessage(mockTranscript, 'voice');
      }, 3000);
      
    } catch (error) {
      message.error('语音录制失败');
      setIsRecording(false);
    }
  };

  /**
   * 停止语音录制
   */
  const handleStopRecording = () => {
    setIsRecording(false);
    console.log('停止语音录制');
  };

  /**
   * 播放音频
   */
  const playAudio = async (audioData: string) => {
    try {
      setIsPlaying(true);
      // 这里实现音频播放逻辑
      console.log('播放音频:', audioData);
      
      // 模拟播放时间
      setTimeout(() => {
        setIsPlaying(false);
      }, 2000);
      
    } catch (error) {
      message.error('音频播放失败');
      setIsPlaying(false);
    }
  };

  /**
   * 清空对话
   */
  const handleClearMessages = () => {
    setMessages([]);
    setSessionId(null);
  };

  /**
   * 导出对话记录
   */
  const handleExportMessages = () => {
    const exportData = {
      sessionId,
      config,
      messages,
      exportTime: new Date().toISOString(),
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json',
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `rag_dialogue_${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  /**
   * 渲染消息
   */
  const renderMessage = (message: Message) => {
    const isUser = message.role === 'user';
    
    return (
      <div
        key={message.id}
        style={{
          display: 'flex',
          justifyContent: isUser ? 'flex-end' : 'flex-start',
          marginBottom: '16px',
        }}
      >
        <div style={{ display: 'flex', maxWidth: '70%', alignItems: 'flex-start' }}>
          {!isUser && (
            <Avatar
              icon={<RobotOutlined />}
              style={{ marginRight: '8px', backgroundColor: '#1890ff' }}
            />
          )}
          
          <div>
            <div
              style={{
                padding: '12px 16px',
                borderRadius: '12px',
                backgroundColor: isUser ? '#1890ff' : '#f5f5f5',
                color: isUser ? 'white' : 'black',
                wordBreak: 'break-word',
              }}
            >
              <Text style={{ color: isUser ? 'white' : 'inherit' }}>
                {message.content}
              </Text>
            </div>
            
            {/* 消息元信息 */}
            <div style={{ marginTop: '4px', fontSize: '12px', color: '#999' }}>
              <Space size="small">
                <span>{new Date(message.timestamp).toLocaleTimeString()}</span>
                {message.confidence && (
                  <Tag size="small">置信度: {(message.confidence * 100).toFixed(0)}%</Tag>
                )}
                {message.processingTime && (
                  <Tag size="small">{message.processingTime}ms</Tag>
                )}
                {message.emotion && (
                  <Tag size="small" color="blue">
                    {message.emotion.type}
                  </Tag>
                )}
                {message.audioData && (
                  <Button
                    type="text"
                    size="small"
                    icon={isPlaying ? <StopOutlined /> : <PlayCircleOutlined />}
                    onClick={() => playAudio(message.audioData!)}
                  />
                )}
              </Space>
            </div>
            
            {/* 知识来源 */}
            {message.sources && message.sources.length > 0 && (
              <div style={{ marginTop: '8px' }}>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  知识来源:
                </Text>
                {message.sources.map((source, index) => (
                  <div key={source.id} style={{ marginTop: '4px' }}>
                    <Tag size="small" color="green">
                      {(source.score * 100).toFixed(0)}%
                    </Tag>
                    <Text style={{ fontSize: '12px', color: '#666' }}>
                      {source.content.substring(0, 50)}...
                    </Text>
                  </div>
                ))}
              </div>
            )}
          </div>
          
          {isUser && (
            <Avatar
              icon={<UserOutlined />}
              style={{ marginLeft: '8px', backgroundColor: '#52c41a' }}
            />
          )}
        </div>
      </div>
    );
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    // 初始化会话
    setSessionId(`session_${Date.now()}`);
  }, []);

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 头部配置 */}
      <Card size="small" style={{ marginBottom: '8px' }}>
        <Row gutter={16} align="middle">
          <Col span={4}>
            <Select
              size="small"
              value={config.knowledgeBaseId}
              onChange={(value) => setConfig(prev => ({ ...prev, knowledgeBaseId: value }))}
              style={{ width: '100%' }}
            >
              <Option value="kb_1">医疗知识库</Option>
              <Option value="kb_2">培训知识库</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              size="small"
              value={config.avatarId}
              onChange={(value) => setConfig(prev => ({ ...prev, avatarId: value }))}
              style={{ width: '100%' }}
            >
              <Option value="avatar_1">小雅医生</Option>
              <Option value="avatar_2">培训师小李</Option>
            </Select>
          </Col>
          <Col span={3}>
            <Space>
              <Text style={{ fontSize: '12px' }}>语音输入</Text>
              <Switch
                size="small"
                checked={config.enableVoiceInput}
                onChange={(checked) => setConfig(prev => ({
                  ...prev,
                  enableVoiceInput: checked
                }))}
              />
            </Space>
          </Col>
          <Col span={3}>
            <Space>
              <Text style={{ fontSize: '12px' }}>语音输出</Text>
              <Switch
                size="small"
                checked={config.enableVoiceOutput}
                onChange={(checked) => setConfig(prev => ({
                  ...prev,
                  enableVoiceOutput: checked
                }))}
              />
            </Space>
          </Col>
          <Col span={6}>
            <Space>
              <Text style={{ fontSize: '12px' }}>搜索阈值</Text>
              <Slider
                size="small"
                min={0.1}
                max={1.0}
                step={0.1}
                value={config.ragSettings.searchThreshold}
                onChange={(value) => setConfig(prev => ({
                  ...prev,
                  ragSettings: { ...prev.ragSettings, searchThreshold: value }
                }))}
                style={{ width: '80px' }}
              />
            </Space>
          </Col>
          <Col span={4}>
            <Space>
              <Button
                size="small"
                icon={<ClearOutlined />}
                onClick={handleClearMessages}
              >
                清空
              </Button>
              <Button
                size="small"
                icon={<DownloadOutlined />}
                onClick={handleExportMessages}
              >
                导出
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 对话区域 */}
      <Card
        title={
          <Space>
            <RobotOutlined />
            <span>RAG对话测试</span>
            {sessionId && (
              <Tag size="small">会话: {sessionId.substring(0, 8)}...</Tag>
            )}
          </Space>
        }
        style={{ flex: 1, display: 'flex', flexDirection: 'column' }}
        bodyStyle={{ flex: 1, display: 'flex', flexDirection: 'column', padding: '16px' }}
      >
        {/* 消息列表 */}
        <div
          style={{
            flex: 1,
            overflowY: 'auto',
            marginBottom: '16px',
            padding: '8px',
          }}
        >
          {messages.length === 0 ? (
            <Empty
              description="开始对话吧"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          ) : (
            messages.map(renderMessage)
          )}
          
          {isLoading && (
            <div style={{ textAlign: 'center', padding: '16px' }}>
              <Spin />
              <Text style={{ marginLeft: '8px', color: '#999' }}>
                正在思考中...
              </Text>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>

        {/* 输入区域 */}
        <div>
          <Space.Compact style={{ width: '100%' }}>
            <TextArea
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              placeholder="输入您的问题..."
              autoSize={{ minRows: 1, maxRows: 3 }}
              onPressEnter={(e) => {
                if (!e.shiftKey) {
                  e.preventDefault();
                  handleSendMessage(inputText);
                }
              }}
              disabled={isLoading}
            />
            <Button
              type="primary"
              icon={<SendOutlined />}
              onClick={() => handleSendMessage(inputText)}
              disabled={isLoading || !inputText.trim()}
            >
              发送
            </Button>
            <Button
              icon={isRecording ? <StopOutlined /> : <SoundOutlined />}
              onClick={isRecording ? handleStopRecording : handleStartRecording}
              disabled={isLoading}
              danger={isRecording}
            >
              {isRecording ? '停止' : '语音'}
            </Button>
          </Space.Compact>
        </div>
      </Card>
      
      <audio ref={audioRef} style={{ display: 'none' }} />
    </div>
  );
};

export default RAGDialoguePanel;
