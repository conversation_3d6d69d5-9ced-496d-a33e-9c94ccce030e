/**
 * 感知系统管理界面
 * 
 * 提供感知系统的配置、监控和调试功能。
 * 支持多模态感知配置、实时数据监控、感知融合可视化等。
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Tabs,
  Switch,
  Slider,
  Form,
  InputNumber,
  Select,
  Button,
  Space,
  Progress,
  Table,
  Tag,
  Alert,
  Statistic,
  Row,
  Col,
  Timeline,
  Tooltip,
  Modal,
  message
} from 'antd';
import {
  EyeOutlined,
  SoundOutlined,
  TeamOutlined,
  EnvironmentOutlined,
  SettingOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  BugOutlined,
  BarChartOutlined
} from '@ant-design/icons';
import { Line, Gauge, Radar } from '@ant-design/plots';
import * as THREE from 'three';
import './PerceptionSystemManager.css';

// 导入感知系统
import { 
  MultiModalPerceptionSystem,
  PerceptionModality,
  PerceptionData,
  FusedPerceptionData
} from '../../../engine/src/ai/perception/MultiModalPerceptionSystem';
import {
  SocialPerceptionSystem,
  SocialEntityDetail,
  GroupInfo
} from '../../../engine/src/ai/perception/SocialPerceptionSystem';

const { TabPane } = Tabs;
const { Option } = Select;

/**
 * 感知配置接口
 */
interface PerceptionConfig {
  visual: {
    enabled: boolean;
    range: number;
    fieldOfView: number;
    resolution: number;
  };
  auditory: {
    enabled: boolean;
    range: number;
    sensitivity: number;
    noiseFilter: boolean;
  };
  social: {
    enabled: boolean;
    range: number;
    relationshipTracking: boolean;
    groupAnalysis: boolean;
  };
  environmental: {
    enabled: boolean;
    weatherSensitive: boolean;
    terrainAnalysis: boolean;
    hazardDetection: boolean;
  };
}

/**
 * 感知统计数据
 */
interface PerceptionStats {
  totalPerceptions: number;
  modalityBreakdown: { [key: string]: number };
  averageConfidence: number;
  processingTime: number;
  anomalies: number;
  fusionRate: number;
}

/**
 * 感知系统管理器组件
 */
const PerceptionSystemManager: React.FC<{
  entityId?: string;
  onConfigChange?: (config: PerceptionConfig) => void;
}> = ({ entityId, onConfigChange }) => {
  // 状态管理
  const [config, setConfig] = useState<PerceptionConfig>({
    visual: {
      enabled: true,
      range: 100,
      fieldOfView: 60,
      resolution: 1.0
    },
    auditory: {
      enabled: true,
      range: 50,
      sensitivity: 0.8,
      noiseFilter: true
    },
    social: {
      enabled: true,
      range: 30,
      relationshipTracking: true,
      groupAnalysis: true
    },
    environmental: {
      enabled: true,
      weatherSensitive: true,
      terrainAnalysis: true,
      hazardDetection: true
    }
  });

  const [isRunning, setIsRunning] = useState(false);
  const [perceptionData, setPerceptionData] = useState<FusedPerceptionData | null>(null);
  const [socialData, setSocialData] = useState<{
    entities: Map<string, SocialEntityDetail>;
    groups: Map<string, GroupInfo>;
  }>({ entities: new Map(), groups: new Map() });
  
  const [stats, setStats] = useState<PerceptionStats>({
    totalPerceptions: 0,
    modalityBreakdown: {},
    averageConfidence: 0,
    processingTime: 0,
    anomalies: 0,
    fusionRate: 0
  });

  const [realtimeData, setRealtimeData] = useState<Array<{
    time: number;
    confidence: number;
    processingTime: number;
  }>>([]);

  // 感知系统实例
  const [perceptionSystem] = useState(() => new MultiModalPerceptionSystem());
  const [socialSystem] = useState(() => new SocialPerceptionSystem(entityId || 'observer'));

  /**
   * 更新配置
   */
  const handleConfigChange = useCallback((modality: keyof PerceptionConfig, key: string, value: any) => {
    const newConfig = {
      ...config,
      [modality]: {
        ...config[modality],
        [key]: value
      }
    };
    
    setConfig(newConfig);
    
    if (onConfigChange) {
      onConfigChange(newConfig);
    }
  }, [config, onConfigChange]);

  /**
   * 启动感知系统
   */
  const handleStart = useCallback(() => {
    setIsRunning(true);
    message.success('感知系统已启动');
    
    // 模拟感知数据更新
    const updateInterval = setInterval(() => {
      if (!isRunning) {
        clearInterval(updateInterval);
        return;
      }
      
      // 模拟感知数据
      const mockData = generateMockPerceptionData();
      setPerceptionData(mockData);
      
      // 更新统计数据
      updateStats(mockData);
      
      // 更新实时数据
      setRealtimeData(prev => {
        const newData = [...prev, {
          time: Date.now(),
          confidence: mockData.confidence,
          processingTime: Math.random() * 10 + 5
        }];
        return newData.slice(-50); // 保留最近50个数据点
      });
      
    }, 1000);
    
    return () => clearInterval(updateInterval);
  }, [isRunning]);

  /**
   * 停止感知系统
   */
  const handleStop = useCallback(() => {
    setIsRunning(false);
    message.info('感知系统已停止');
  }, []);

  /**
   * 生成模拟感知数据
   */
  const generateMockPerceptionData = (): FusedPerceptionData => {
    return {
      timestamp: Date.now(),
      confidence: Math.random() * 0.4 + 0.6, // 0.6-1.0
      worldModel: {
        entities: new Map(),
        environment: {
          layout: {},
          lighting: {
            intensity: Math.random(),
            direction: new THREE.Vector3(0, -1, 0),
            color: new THREE.Color(1, 1, 1),
            shadows: true
          },
          weather: {},
          obstacles: [],
          resources: [],
          hazards: []
        },
        social: {
          relationships: new Map(),
          groups: [],
          hierarchies: [],
          norms: []
        },
        temporal: {
          currentTime: Date.now(),
          timeOfDay: 'day',
          schedule: [],
          patterns: []
        }
      },
      attentionFocus: [],
      predictions: [],
      anomalies: Math.random() > 0.8 ? [{
        type: 'unusual_behavior',
        description: '检测到异常行为模式',
        severity: Math.random(),
        timestamp: Date.now()
      }] : []
    };
  };

  /**
   * 更新统计数据
   */
  const updateStats = useCallback((data: FusedPerceptionData) => {
    setStats(prev => ({
      totalPerceptions: prev.totalPerceptions + 1,
      modalityBreakdown: {
        visual: (prev.modalityBreakdown.visual || 0) + (config.visual.enabled ? 1 : 0),
        auditory: (prev.modalityBreakdown.auditory || 0) + (config.auditory.enabled ? 1 : 0),
        social: (prev.modalityBreakdown.social || 0) + (config.social.enabled ? 1 : 0),
        environmental: (prev.modalityBreakdown.environmental || 0) + (config.environmental.enabled ? 1 : 0)
      },
      averageConfidence: (prev.averageConfidence * prev.totalPerceptions + data.confidence) / (prev.totalPerceptions + 1),
      processingTime: Math.random() * 10 + 5,
      anomalies: prev.anomalies + data.anomalies.length,
      fusionRate: Math.random() * 0.2 + 0.8
    }));
  }, [config]);

  /**
   * 渲染配置面板
   */
  const renderConfigPanel = () => (
    <Card title="感知配置" size="small">
      <Tabs defaultActiveKey="visual">
        <TabPane tab={<span><EyeOutlined />视觉</span>} key="visual">
          <Form layout="vertical" size="small">
            <Form.Item label="启用视觉感知">
              <Switch
                checked={config.visual.enabled}
                onChange={(checked) => handleConfigChange('visual', 'enabled', checked)}
              />
            </Form.Item>
            <Form.Item label={`感知范围: ${config.visual.range}m`}>
              <Slider
                min={10}
                max={200}
                value={config.visual.range}
                onChange={(value) => handleConfigChange('visual', 'range', value)}
                disabled={!config.visual.enabled}
              />
            </Form.Item>
            <Form.Item label={`视野角度: ${config.visual.fieldOfView}°`}>
              <Slider
                min={30}
                max={180}
                value={config.visual.fieldOfView}
                onChange={(value) => handleConfigChange('visual', 'fieldOfView', value)}
                disabled={!config.visual.enabled}
              />
            </Form.Item>
            <Form.Item label={`分辨率: ${config.visual.resolution}`}>
              <Slider
                min={0.1}
                max={2.0}
                step={0.1}
                value={config.visual.resolution}
                onChange={(value) => handleConfigChange('visual', 'resolution', value)}
                disabled={!config.visual.enabled}
              />
            </Form.Item>
          </Form>
        </TabPane>
        
        <TabPane tab={<span><SoundOutlined />听觉</span>} key="auditory">
          <Form layout="vertical" size="small">
            <Form.Item label="启用听觉感知">
              <Switch
                checked={config.auditory.enabled}
                onChange={(checked) => handleConfigChange('auditory', 'enabled', checked)}
              />
            </Form.Item>
            <Form.Item label={`听觉范围: ${config.auditory.range}m`}>
              <Slider
                min={5}
                max={100}
                value={config.auditory.range}
                onChange={(value) => handleConfigChange('auditory', 'range', value)}
                disabled={!config.auditory.enabled}
              />
            </Form.Item>
            <Form.Item label={`敏感度: ${config.auditory.sensitivity}`}>
              <Slider
                min={0.1}
                max={1.0}
                step={0.1}
                value={config.auditory.sensitivity}
                onChange={(value) => handleConfigChange('auditory', 'sensitivity', value)}
                disabled={!config.auditory.enabled}
              />
            </Form.Item>
            <Form.Item label="噪音过滤">
              <Switch
                checked={config.auditory.noiseFilter}
                onChange={(checked) => handleConfigChange('auditory', 'noiseFilter', checked)}
                disabled={!config.auditory.enabled}
              />
            </Form.Item>
          </Form>
        </TabPane>
        
        <TabPane tab={<span><TeamOutlined />社交</span>} key="social">
          <Form layout="vertical" size="small">
            <Form.Item label="启用社交感知">
              <Switch
                checked={config.social.enabled}
                onChange={(checked) => handleConfigChange('social', 'enabled', checked)}
              />
            </Form.Item>
            <Form.Item label={`社交范围: ${config.social.range}m`}>
              <Slider
                min={5}
                max={50}
                value={config.social.range}
                onChange={(value) => handleConfigChange('social', 'range', value)}
                disabled={!config.social.enabled}
              />
            </Form.Item>
            <Form.Item label="关系跟踪">
              <Switch
                checked={config.social.relationshipTracking}
                onChange={(checked) => handleConfigChange('social', 'relationshipTracking', checked)}
                disabled={!config.social.enabled}
              />
            </Form.Item>
            <Form.Item label="群体分析">
              <Switch
                checked={config.social.groupAnalysis}
                onChange={(checked) => handleConfigChange('social', 'groupAnalysis', checked)}
                disabled={!config.social.enabled}
              />
            </Form.Item>
          </Form>
        </TabPane>
        
        <TabPane tab={<span><EnvironmentOutlined />环境</span>} key="environmental">
          <Form layout="vertical" size="small">
            <Form.Item label="启用环境感知">
              <Switch
                checked={config.environmental.enabled}
                onChange={(checked) => handleConfigChange('environmental', 'enabled', checked)}
              />
            </Form.Item>
            <Form.Item label="天气敏感">
              <Switch
                checked={config.environmental.weatherSensitive}
                onChange={(checked) => handleConfigChange('environmental', 'weatherSensitive', checked)}
                disabled={!config.environmental.enabled}
              />
            </Form.Item>
            <Form.Item label="地形分析">
              <Switch
                checked={config.environmental.terrainAnalysis}
                onChange={(checked) => handleConfigChange('environmental', 'terrainAnalysis', checked)}
                disabled={!config.environmental.enabled}
              />
            </Form.Item>
            <Form.Item label="危险检测">
              <Switch
                checked={config.environmental.hazardDetection}
                onChange={(checked) => handleConfigChange('environmental', 'hazardDetection', checked)}
                disabled={!config.environmental.enabled}
              />
            </Form.Item>
          </Form>
        </TabPane>
      </Tabs>
    </Card>
  );

  /**
   * 渲染监控面板
   */
  const renderMonitorPanel = () => (
    <Card title="实时监控" size="small">
      <Row gutter={16}>
        <Col span={6}>
          <Statistic
            title="总感知次数"
            value={stats.totalPerceptions}
            prefix={<EyeOutlined />}
          />
        </Col>
        <Col span={6}>
          <Statistic
            title="平均置信度"
            value={stats.averageConfidence}
            precision={2}
            suffix="%"
            valueStyle={{ color: stats.averageConfidence > 0.8 ? '#3f8600' : '#cf1322' }}
          />
        </Col>
        <Col span={6}>
          <Statistic
            title="处理时间"
            value={stats.processingTime}
            precision={1}
            suffix="ms"
          />
        </Col>
        <Col span={6}>
          <Statistic
            title="异常数量"
            value={stats.anomalies}
            valueStyle={{ color: stats.anomalies > 0 ? '#cf1322' : '#3f8600' }}
          />
        </Col>
      </Row>
      
      {realtimeData.length > 0 && (
        <div style={{ marginTop: 16 }}>
          <Line
            data={realtimeData}
            xField="time"
            yField="confidence"
            height={200}
            smooth
            point={{ size: 2 }}
            xAxis={{
              type: 'time',
              tickCount: 5
            }}
            yAxis={{
              min: 0,
              max: 1
            }}
          />
        </div>
      )}
    </Card>
  );

  /**
   * 渲染感知数据面板
   */
  const renderDataPanel = () => {
    if (!perceptionData) {
      return (
        <Card title="感知数据" size="small">
          <Alert message="暂无感知数据" type="info" />
        </Card>
      );
    }

    return (
      <Card title="感知数据" size="small">
        <Tabs defaultActiveKey="overview">
          <TabPane tab="概览" key="overview">
            <Row gutter={16}>
              <Col span={12}>
                <Card title="置信度" size="small">
                  <Gauge
                    percent={perceptionData.confidence}
                    range={{ color: '#30BF78' }}
                    indicator={{
                      pointer: { style: { stroke: '#D0D0D0' } },
                      pin: { style: { stroke: '#D0D0D0' } }
                    }}
                    statistic={{
                      content: {
                        style: { fontSize: '16px', lineHeight: '16px' }
                      }
                    }}
                  />
                </Card>
              </Col>
              <Col span={12}>
                <Card title="注意力焦点" size="small">
                  {perceptionData.attentionFocus.length > 0 ? (
                    <Timeline size="small">
                      {perceptionData.attentionFocus.map((focus, index) => (
                        <Timeline.Item key={index}>
                          <div>
                            <Tag color="blue">{focus.type}</Tag>
                            <span>{focus.target}</span>
                          </div>
                          <div style={{ fontSize: '12px', color: '#666' }}>
                            {focus.reason}
                          </div>
                        </Timeline.Item>
                      ))}
                    </Timeline>
                  ) : (
                    <div style={{ textAlign: 'center', color: '#999' }}>
                      暂无注意力焦点
                    </div>
                  )}
                </Card>
              </Col>
            </Row>
          </TabPane>
          
          <TabPane tab="异常" key="anomalies">
            {perceptionData.anomalies.length > 0 ? (
              <Timeline>
                {perceptionData.anomalies.map((anomaly, index) => (
                  <Timeline.Item 
                    key={index}
                    color={anomaly.severity > 0.7 ? 'red' : anomaly.severity > 0.4 ? 'orange' : 'blue'}
                  >
                    <div>
                      <Tag color={anomaly.severity > 0.7 ? 'red' : anomaly.severity > 0.4 ? 'orange' : 'blue'}>
                        {anomaly.type}
                      </Tag>
                      <span>{anomaly.description}</span>
                    </div>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      严重程度: {(anomaly.severity * 100).toFixed(1)}%
                    </div>
                  </Timeline.Item>
                ))}
              </Timeline>
            ) : (
              <Alert message="未检测到异常" type="success" />
            )}
          </TabPane>
          
          <TabPane tab="预测" key="predictions">
            {perceptionData.predictions.length > 0 ? (
              <Table
                dataSource={perceptionData.predictions}
                columns={[
                  { title: '类型', dataIndex: 'type', key: 'type' },
                  { title: '目标', dataIndex: 'target', key: 'target' },
                  { 
                    title: '置信度', 
                    dataIndex: 'confidence', 
                    key: 'confidence',
                    render: (value) => `${(value * 100).toFixed(1)}%`
                  },
                  { 
                    title: '时间范围', 
                    dataIndex: 'timeHorizon', 
                    key: 'timeHorizon',
                    render: (value) => `${value}ms`
                  }
                ]}
                size="small"
                pagination={false}
              />
            ) : (
              <Alert message="暂无预测数据" type="info" />
            )}
          </TabPane>
        </Tabs>
      </Card>
    );
  };

  /**
   * 渲染控制面板
   */
  const renderControlPanel = () => (
    <Card title="控制面板" size="small">
      <Space>
        <Button
          type="primary"
          icon={isRunning ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
          onClick={isRunning ? handleStop : handleStart}
        >
          {isRunning ? '停止' : '启动'}
        </Button>
        
        <Button
          icon={<BugOutlined />}
          onClick={() => {
            Modal.info({
              title: '调试信息',
              content: (
                <pre style={{ maxHeight: 400, overflow: 'auto' }}>
                  {JSON.stringify({ config, stats, perceptionData }, null, 2)}
                </pre>
              ),
              width: 800
            });
          }}
        >
          调试
        </Button>
        
        <Button
          icon={<BarChartOutlined />}
          onClick={() => {
            // 导出统计数据
            const dataStr = JSON.stringify(stats, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'perception_stats.json';
            link.click();
            URL.revokeObjectURL(url);
            message.success('统计数据已导出');
          }}
        >
          导出数据
        </Button>
      </Space>
    </Card>
  );

  return (
    <div className="perception-system-manager">
      <Row gutter={16}>
        <Col span={8}>
          {renderControlPanel()}
          {renderConfigPanel()}
        </Col>
        <Col span={16}>
          {renderMonitorPanel()}
          {renderDataPanel()}
        </Col>
      </Row>
    </div>
  );
};

export default PerceptionSystemManager;
