/**
 * TreeView.tsx
 * 
 * 树形控件React组件
 */

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { Tree, Input, Checkbox, Button, Dropdown, Menu, Tooltip } from 'antd';
import {
  SearchOutlined,
  ExpandOutlined,
  CompressOutlined,
  MoreOutlined,
  FolderOutlined,
  FolderOpenOutlined,
  FileOutlined
} from '@ant-design/icons';
import type { TreeProps, DataNode } from 'antd/es/tree';
import './TreeView.module.css';

const { Search } = Input;

/**
 * 树节点数据接口
 */
export interface TreeNodeData {
  key: string;
  title: string;
  icon?: React.ReactNode;
  children?: TreeNodeData[];
  isLeaf?: boolean;
  disabled?: boolean;
  disableCheckbox?: boolean;
  selectable?: boolean;
  checkable?: boolean;
  data?: any;
  className?: string;
  style?: React.CSSProperties;
}

/**
 * 树形控件配置
 */
export interface TreeViewConfig {
  /** 是否显示连接线 */
  showLine?: boolean;
  /** 是否显示图标 */
  showIcon?: boolean;
  /** 是否支持多选 */
  multiple?: boolean;
  /** 是否支持复选框 */
  checkable?: boolean;
  /** 是否可拖拽 */
  draggable?: boolean;
  /** 是否支持搜索 */
  searchable?: boolean;
  /** 是否虚拟滚动 */
  virtual?: boolean;
  /** 树高度 */
  height?: number;
  /** 默认展开所有节点 */
  defaultExpandAll?: boolean;
  /** 默认展开的节点 */
  defaultExpandedKeys?: string[];
  /** 默认选中的节点 */
  defaultSelectedKeys?: string[];
  /** 默认勾选的节点 */
  defaultCheckedKeys?: string[];
  /** 严格模式（父子节点选中状态不再关联） */
  checkStrictly?: boolean;
}

/**
 * 树形控件事件
 */
export interface TreeViewEvents {
  onSelect?: (selectedKeys: string[], info: any) => void;
  onCheck?: (checkedKeys: string[] | { checked: string[]; halfChecked: string[] }, info: any) => void;
  onExpand?: (expandedKeys: string[], info: any) => void;
  onRightClick?: (info: any) => void;
  onDragStart?: (info: any) => void;
  onDragEnter?: (info: any) => void;
  onDragOver?: (info: any) => void;
  onDragLeave?: (info: any) => void;
  onDragEnd?: (info: any) => void;
  onDrop?: (info: any) => void;
  onLoad?: (loadedKeys: string[], info: any) => void;
  onSearch?: (value: string, matchedKeys: string[]) => void;
}

/**
 * 树形控件属性
 */
export interface TreeViewProps {
  /** 树数据 */
  treeData: TreeNodeData[];
  /** 配置选项 */
  config?: TreeViewConfig;
  /** 事件处理 */
  events?: TreeViewEvents;
  /** 样式类名 */
  className?: string;
  /** 样式 */
  style?: React.CSSProperties;
  /** 加载状态 */
  loading?: boolean;
  /** 空状态渲染 */
  emptyRender?: () => React.ReactNode;
  /** 自定义节点渲染 */
  titleRender?: (nodeData: TreeNodeData) => React.ReactNode;
  /** 工具栏渲染 */
  toolbarRender?: () => React.ReactNode;
  /** 是否显示工具栏 */
  showToolbar?: boolean;
}

/**
 * 默认配置
 */
const DEFAULT_CONFIG: TreeViewConfig = {
  showLine: true,
  showIcon: true,
  multiple: false,
  checkable: false,
  draggable: false,
  searchable: true,
  virtual: true,
  height: 400,
  defaultExpandAll: false,
  checkStrictly: false
};

/**
 * 树形控件组件
 */
export const TreeView: React.FC<TreeViewProps> = ({
  treeData,
  config: userConfig,
  events,
  className,
  style,
  loading = false,
  emptyRender,
  titleRender,
  toolbarRender,
  showToolbar = true
}) => {
  const config = useMemo(() => ({ ...DEFAULT_CONFIG, ...userConfig }), [userConfig]);
  
  const [expandedKeys, setExpandedKeys] = useState<string[]>(config.defaultExpandedKeys || []);
  const [selectedKeys, setSelectedKeys] = useState<string[]>(config.defaultSelectedKeys || []);
  const [checkedKeys, setCheckedKeys] = useState<string[] | { checked: string[]; halfChecked: string[] }>(
    config.defaultCheckedKeys || []
  );
  const [searchValue, setSearchValue] = useState('');
  const [autoExpandParent, setAutoExpandParent] = useState(true);
  const [matchedKeys, setMatchedKeys] = useState<string[]>([]);

  const treeRef = useRef<any>(null);

  // 转换树数据格式
  const convertTreeData = useCallback((data: TreeNodeData[]): DataNode[] => {
    return data.map(node => ({
      key: node.key,
      title: titleRender ? titleRender(node) : node.title,
      icon: node.icon || (node.isLeaf ? <FileOutlined /> : 
        expandedKeys.includes(node.key) ? <FolderOpenOutlined /> : <FolderOutlined />),
      children: node.children ? convertTreeData(node.children) : undefined,
      isLeaf: node.isLeaf,
      disabled: node.disabled,
      disableCheckbox: node.disableCheckbox,
      selectable: node.selectable,
      checkable: node.checkable,
      className: node.className,
      style: node.style
    }));
  }, [expandedKeys, titleRender]);

  // 搜索功能
  const searchTree = useCallback((value: string) => {
    const searchInTree = (nodes: TreeNodeData[], matches: string[] = []): string[] => {
      nodes.forEach(node => {
        if (node.title.toLowerCase().includes(value.toLowerCase())) {
          matches.push(node.key);
        }
        if (node.children) {
          searchInTree(node.children, matches);
        }
      });
      return matches;
    };

    if (value) {
      const matches = searchInTree(treeData);
      setMatchedKeys(matches);
      setExpandedKeys(matches);
      setAutoExpandParent(true);
      events?.onSearch?.(value, matches);
    } else {
      setMatchedKeys([]);
      setAutoExpandParent(false);
      events?.onSearch?.(value, []);
    }
  }, [treeData, events]);

  // 处理搜索
  const handleSearch = useCallback((value: string) => {
    setSearchValue(value);
    searchTree(value);
  }, [searchTree]);

  // 处理展开
  const handleExpand = useCallback((keys: string[], info: any) => {
    setExpandedKeys(keys);
    setAutoExpandParent(false);
    events?.onExpand?.(keys, info);
  }, [events]);

  // 处理选择
  const handleSelect = useCallback((keys: string[], info: any) => {
    setSelectedKeys(keys);
    events?.onSelect?.(keys, info);
  }, [events]);

  // 处理勾选
  const handleCheck = useCallback((keys: string[] | { checked: string[]; halfChecked: string[] }, info: any) => {
    setCheckedKeys(keys);
    events?.onCheck?.(keys, info);
  }, [events]);

  // 展开所有节点
  const expandAll = useCallback(() => {
    const getAllKeys = (nodes: TreeNodeData[]): string[] => {
      let keys: string[] = [];
      nodes.forEach(node => {
        if (node.children && node.children.length > 0) {
          keys.push(node.key);
          keys = keys.concat(getAllKeys(node.children));
        }
      });
      return keys;
    };

    const allKeys = getAllKeys(treeData);
    setExpandedKeys(allKeys);
  }, [treeData]);

  // 折叠所有节点
  const collapseAll = useCallback(() => {
    setExpandedKeys([]);
  }, []);

  // 获取选中节点数据
  const getSelectedNodes = useCallback(() => {
    const findNodes = (nodes: TreeNodeData[], keys: string[]): TreeNodeData[] => {
      const result: TreeNodeData[] = [];
      nodes.forEach(node => {
        if (keys.includes(node.key)) {
          result.push(node);
        }
        if (node.children) {
          result.push(...findNodes(node.children, keys));
        }
      });
      return result;
    };

    return findNodes(treeData, selectedKeys);
  }, [treeData, selectedKeys]);

  // 获取勾选节点数据
  const getCheckedNodes = useCallback(() => {
    const keys = Array.isArray(checkedKeys) ? checkedKeys : checkedKeys.checked;
    const findNodes = (nodes: TreeNodeData[], keys: string[]): TreeNodeData[] => {
      const result: TreeNodeData[] = [];
      nodes.forEach(node => {
        if (keys.includes(node.key)) {
          result.push(node);
        }
        if (node.children) {
          result.push(...findNodes(node.children, keys));
        }
      });
      return result;
    };

    return findNodes(treeData, keys);
  }, [treeData, checkedKeys]);

  // 滚动到节点
  const scrollToNode = useCallback((key: string) => {
    if (treeRef.current) {
      treeRef.current.scrollTo({ key });
    }
  }, []);

  // 右键菜单
  const contextMenu = useMemo(() => (
    <Menu>
      <Menu.Item key="expand" onClick={expandAll}>
        <ExpandOutlined /> 展开所有
      </Menu.Item>
      <Menu.Item key="collapse" onClick={collapseAll}>
        <CompressOutlined /> 折叠所有
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item key="refresh">
        刷新
      </Menu.Item>
    </Menu>
  ), [expandAll, collapseAll]);

  // 默认工具栏
  const defaultToolbar = useMemo(() => (
    <div className="tree-toolbar">
      {config.searchable && (
        <Search
          placeholder="搜索节点"
          value={searchValue}
          onChange={(e) => handleSearch(e.target.value)}
          style={{ width: 200, marginRight: 8 }}
          allowClear
        />
      )}
      <Button.Group size="small">
        <Tooltip title="展开所有">
          <Button icon={<ExpandOutlined />} onClick={expandAll} />
        </Tooltip>
        <Tooltip title="折叠所有">
          <Button icon={<CompressOutlined />} onClick={collapseAll} />
        </Tooltip>
      </Button.Group>
      <Dropdown overlay={contextMenu} trigger={['click']}>
        <Button size="small" icon={<MoreOutlined />} style={{ marginLeft: 8 }} />
      </Dropdown>
    </div>
  ), [config.searchable, searchValue, handleSearch, expandAll, collapseAll, contextMenu]);

  // 高亮搜索结果
  const titleRenderWithHighlight = useCallback((nodeData: TreeNodeData) => {
    if (titleRender) {
      return titleRender(nodeData);
    }

    const title = nodeData.title;
    if (searchValue && matchedKeys.includes(nodeData.key)) {
      const index = title.toLowerCase().indexOf(searchValue.toLowerCase());
      if (index !== -1) {
        const beforeStr = title.substring(0, index);
        const matchStr = title.substring(index, index + searchValue.length);
        const afterStr = title.substring(index + searchValue.length);
        
        return (
          <span>
            {beforeStr}
            <span style={{ color: '#f50', backgroundColor: '#ffd591' }}>{matchStr}</span>
            {afterStr}
          </span>
        );
      }
    }
    
    return title;
  }, [titleRender, searchValue, matchedKeys]);

  const convertedTreeData = useMemo(() => convertTreeData(treeData), [convertTreeData, treeData]);

  if (loading) {
    return (
      <div className={`tree-view-container loading ${className || ''}`} style={style}>
        <div className="tree-loading">加载中...</div>
      </div>
    );
  }

  if (treeData.length === 0) {
    return (
      <div className={`tree-view-container empty ${className || ''}`} style={style}>
        {emptyRender ? emptyRender() : <div className="tree-empty">暂无数据</div>}
      </div>
    );
  }

  return (
    <div className={`tree-view-container ${className || ''}`} style={style}>
      {showToolbar && (toolbarRender ? toolbarRender() : defaultToolbar)}
      
      <div className="tree-content" style={{ height: config.height }}>
        <Tree
          ref={treeRef}
          treeData={convertedTreeData.map(node => ({
            ...node,
            title: titleRenderWithHighlight(treeData.find(d => d.key === node.key)!)
          }))}
          showLine={config.showLine}
          showIcon={config.showIcon}
          multiple={config.multiple}
          checkable={config.checkable}
          draggable={config.draggable}
          virtual={config.virtual}
          height={config.height}
          expandedKeys={expandedKeys}
          selectedKeys={selectedKeys}
          checkedKeys={checkedKeys}
          autoExpandParent={autoExpandParent}
          checkStrictly={config.checkStrictly}
          onExpand={handleExpand}
          onSelect={handleSelect}
          onCheck={config.checkable ? handleCheck : undefined}
          onRightClick={events?.onRightClick}
          onDragStart={config.draggable ? events?.onDragStart : undefined}
          onDragEnter={config.draggable ? events?.onDragEnter : undefined}
          onDragOver={config.draggable ? events?.onDragOver : undefined}
          onDragLeave={config.draggable ? events?.onDragLeave : undefined}
          onDragEnd={config.draggable ? events?.onDragEnd : undefined}
          onDrop={config.draggable ? events?.onDrop : undefined}
          onLoad={events?.onLoad}
        />
      </div>
    </div>
  );
};

// 导出工具函数
export const TreeViewUtils = {
  /**
   * 查找节点
   */
  findNode: (treeData: TreeNodeData[], key: string): TreeNodeData | null => {
    for (const node of treeData) {
      if (node.key === key) {
        return node;
      }
      if (node.children) {
        const found = TreeViewUtils.findNode(node.children, key);
        if (found) return found;
      }
    }
    return null;
  },

  /**
   * 获取所有叶子节点
   */
  getLeafNodes: (treeData: TreeNodeData[]): TreeNodeData[] => {
    const leaves: TreeNodeData[] = [];
    const traverse = (nodes: TreeNodeData[]) => {
      nodes.forEach(node => {
        if (!node.children || node.children.length === 0) {
          leaves.push(node);
        } else {
          traverse(node.children);
        }
      });
    };
    traverse(treeData);
    return leaves;
  },

  /**
   * 获取节点路径
   */
  getNodePath: (treeData: TreeNodeData[], targetKey: string): TreeNodeData[] => {
    const path: TreeNodeData[] = [];
    const findPath = (nodes: TreeNodeData[], target: string): boolean => {
      for (const node of nodes) {
        path.push(node);
        if (node.key === target) {
          return true;
        }
        if (node.children && findPath(node.children, target)) {
          return true;
        }
        path.pop();
      }
      return false;
    };
    findPath(treeData, targetKey);
    return path;
  },

  /**
   * 过滤树数据
   */
  filterTree: (treeData: TreeNodeData[], predicate: (node: TreeNodeData) => boolean): TreeNodeData[] => {
    const filter = (nodes: TreeNodeData[]): TreeNodeData[] => {
      return nodes.reduce((acc: TreeNodeData[], node) => {
        const filteredChildren = node.children ? filter(node.children) : undefined;
        if (predicate(node) || (filteredChildren && filteredChildren.length > 0)) {
          acc.push({
            ...node,
            children: filteredChildren
          });
        }
        return acc;
      }, []);
    };
    return filter(treeData);
  }
};

export default TreeView;
