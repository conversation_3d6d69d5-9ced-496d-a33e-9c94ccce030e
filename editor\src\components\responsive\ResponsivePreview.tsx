/**
 * ResponsivePreview.tsx
 * 
 * 响应式设计预览组件，支持多设备尺寸预览
 */

import React, { useState, useRef, useCallback, useEffect, useMemo } from 'react';
import {
  Button,
  Select,
  Slider,
  InputNumber,
  Space,
  Tooltip,
  Dropdown,
  Menu,
  Switch,
  Badge,
  Divider
} from 'antd';
import {
  MobileOutlined,
  TabletOutlined,
  DesktopOutlined,
  LaptopOutlined,
  RotateLeftOutlined,
  ExpandOutlined,
  CompressOutlined,
  EyeOutlined,
  SettingOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import './ResponsivePreview.module.css';

/**
 * 设备预设配置
 */
export interface DevicePreset {
  id: string;
  name: string;
  category: 'mobile' | 'tablet' | 'laptop' | 'desktop';
  width: number;
  height: number;
  pixelRatio: number;
  userAgent?: string;
  icon: React.ReactNode;
}

/**
 * 预设设备列表
 */
const DEVICE_PRESETS: DevicePreset[] = [
  // 手机设备
  {
    id: 'iphone-14',
    name: 'iPhone 14',
    category: 'mobile',
    width: 390,
    height: 844,
    pixelRatio: 3,
    icon: <MobileOutlined />
  },
  {
    id: 'iphone-14-plus',
    name: 'iPhone 14 Plus',
    category: 'mobile',
    width: 428,
    height: 926,
    pixelRatio: 3,
    icon: <MobileOutlined />
  },
  {
    id: 'samsung-s23',
    name: 'Samsung Galaxy S23',
    category: 'mobile',
    width: 360,
    height: 780,
    pixelRatio: 3,
    icon: <MobileOutlined />
  },
  {
    id: 'pixel-7',
    name: 'Google Pixel 7',
    category: 'mobile',
    width: 412,
    height: 915,
    pixelRatio: 2.625,
    icon: <MobileOutlined />
  },
  // 平板设备
  {
    id: 'ipad-air',
    name: 'iPad Air',
    category: 'tablet',
    width: 820,
    height: 1180,
    pixelRatio: 2,
    icon: <TabletOutlined />
  },
  {
    id: 'ipad-pro-11',
    name: 'iPad Pro 11"',
    category: 'tablet',
    width: 834,
    height: 1194,
    pixelRatio: 2,
    icon: <TabletOutlined />
  },
  {
    id: 'surface-pro',
    name: 'Surface Pro',
    category: 'tablet',
    width: 912,
    height: 1368,
    pixelRatio: 2,
    icon: <TabletOutlined />
  },
  // 笔记本电脑
  {
    id: 'macbook-air',
    name: 'MacBook Air',
    category: 'laptop',
    width: 1440,
    height: 900,
    pixelRatio: 2,
    icon: <LaptopOutlined />
  },
  {
    id: 'macbook-pro-14',
    name: 'MacBook Pro 14"',
    category: 'laptop',
    width: 1512,
    height: 982,
    pixelRatio: 2,
    icon: <LaptopOutlined />
  },
  // 桌面显示器
  {
    id: 'desktop-1080p',
    name: '1080p Desktop',
    category: 'desktop',
    width: 1920,
    height: 1080,
    pixelRatio: 1,
    icon: <DesktopOutlined />
  },
  {
    id: 'desktop-1440p',
    name: '1440p Desktop',
    category: 'desktop',
    width: 2560,
    height: 1440,
    pixelRatio: 1,
    icon: <DesktopOutlined />
  },
  {
    id: 'desktop-4k',
    name: '4K Desktop',
    category: 'desktop',
    width: 3840,
    height: 2160,
    pixelRatio: 1,
    icon: <DesktopOutlined />
  }
];

/**
 * 响应式断点
 */
const BREAKPOINTS = {
  xs: 480,
  sm: 576,
  md: 768,
  lg: 992,
  xl: 1200,
  xxl: 1600
};

/**
 * 响应式预览属性
 */
export interface ResponsivePreviewProps {
  /** 预览内容 */
  children: React.ReactNode;
  /** 初始设备ID */
  initialDeviceId?: string;
  /** 是否显示设备边框 */
  showDeviceFrame?: boolean;
  /** 是否显示标尺 */
  showRuler?: boolean;
  /** 设备变化回调 */
  onDeviceChange?: (device: DevicePreset) => void;
  /** 尺寸变化回调 */
  onSizeChange?: (width: number, height: number) => void;
  /** 样式类名 */
  className?: string;
}

/**
 * 响应式预览组件
 */
export const ResponsivePreview: React.FC<ResponsivePreviewProps> = ({
  children,
  initialDeviceId = 'desktop-1080p',
  showDeviceFrame = true,
  showRuler = true,
  onDeviceChange,
  onSizeChange,
  className
}) => {
  const [selectedDeviceId, setSelectedDeviceId] = useState(initialDeviceId);
  const [isLandscape, setIsLandscape] = useState(false);
  const [customWidth, setCustomWidth] = useState(1920);
  const [customHeight, setCustomHeight] = useState(1080);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [showFrame, setShowFrame] = useState(showDeviceFrame);
  const [showRulers, setShowRulers] = useState(showRuler);
  const [isFullscreen, setIsFullscreen] = useState(false);

  const previewRef = useRef<HTMLDivElement>(null);
  const frameRef = useRef<HTMLDivElement>(null);

  // 获取当前设备配置
  const currentDevice = useMemo(() => {
    return DEVICE_PRESETS.find(device => device.id === selectedDeviceId) || DEVICE_PRESETS[0];
  }, [selectedDeviceId]);

  // 计算实际显示尺寸
  const displaySize = useMemo(() => {
    let width = currentDevice.width;
    let height = currentDevice.height;

    if (isLandscape) {
      [width, height] = [height, width];
    }

    return { width, height };
  }, [currentDevice, isLandscape]);

  // 获取当前断点
  const currentBreakpoint = useMemo(() => {
    const width = displaySize.width;
    if (width < BREAKPOINTS.xs) return 'xs';
    if (width < BREAKPOINTS.sm) return 'sm';
    if (width < BREAKPOINTS.md) return 'md';
    if (width < BREAKPOINTS.lg) return 'lg';
    if (width < BREAKPOINTS.xl) return 'xl';
    return 'xxl';
  }, [displaySize.width]);

  // 处理设备切换
  const handleDeviceChange = useCallback((deviceId: string) => {
    setSelectedDeviceId(deviceId);
    const device = DEVICE_PRESETS.find(d => d.id === deviceId);
    if (device) {
      onDeviceChange?.(device);
      onSizeChange?.(device.width, device.height);
    }
  }, [onDeviceChange, onSizeChange]);

  // 处理方向切换
  const handleOrientationToggle = useCallback(() => {
    setIsLandscape(!isLandscape);
    const newWidth = isLandscape ? currentDevice.width : currentDevice.height;
    const newHeight = isLandscape ? currentDevice.height : currentDevice.width;
    onSizeChange?.(newWidth, newHeight);
  }, [isLandscape, currentDevice, onSizeChange]);

  // 处理自定义尺寸
  const handleCustomSize = useCallback((width: number, height: number) => {
    setCustomWidth(width);
    setCustomHeight(height);
    onSizeChange?.(width, height);
  }, [onSizeChange]);

  // 处理全屏切换
  const handleFullscreenToggle = useCallback(() => {
    if (!isFullscreen) {
      previewRef.current?.requestFullscreen?.();
    } else {
      document.exitFullscreen?.();
    }
  }, [isFullscreen]);

  // 监听全屏状态变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);

  // 自动调整缩放级别
  const handleAutoZoom = useCallback(() => {
    if (!previewRef.current || !frameRef.current) return;

    const containerRect = previewRef.current.getBoundingClientRect();
    const frameWidth = displaySize.width;
    const frameHeight = displaySize.height;

    const scaleX = (containerRect.width - 100) / frameWidth;
    const scaleY = (containerRect.height - 100) / frameHeight;
    const scale = Math.min(scaleX, scaleY, 1);

    setZoomLevel(scale);
  }, [displaySize]);

  // 设备分类菜单
  const deviceMenu = (
    <Menu>
      {Object.entries(
        DEVICE_PRESETS.reduce((acc, device) => {
          if (!acc[device.category]) acc[device.category] = [];
          acc[device.category].push(device);
          return acc;
        }, {} as Record<string, DevicePreset[]>)
      ).map(([category, devices]) => (
        <Menu.SubMenu
          key={category}
          title={
            <span>
              {category === 'mobile' && <MobileOutlined />}
              {category === 'tablet' && <TabletOutlined />}
              {category === 'laptop' && <LaptopOutlined />}
              {category === 'desktop' && <DesktopOutlined />}
              <span style={{ marginLeft: 8, textTransform: 'capitalize' }}>{category}</span>
            </span>
          }
        >
          {devices.map(device => (
            <Menu.Item
              key={device.id}
              onClick={() => handleDeviceChange(device.id)}
              className={selectedDeviceId === device.id ? 'selected' : ''}
            >
              <Space>
                {device.icon}
                <span>{device.name}</span>
                <span style={{ color: '#999', fontSize: '12px' }}>
                  {device.width} × {device.height}
                </span>
              </Space>
            </Menu.Item>
          ))}
        </Menu.SubMenu>
      ))}
      <Menu.Divider />
      <Menu.Item key="custom">
        <Space>
          <SettingOutlined />
          <span>自定义尺寸</span>
        </Space>
      </Menu.Item>
    </Menu>
  );

  return (
    <div className={`responsive-preview ${className || ''}`} ref={previewRef}>
      {/* 工具栏 */}
      <div className="preview-toolbar">
        <Space>
          {/* 设备选择 */}
          <Dropdown overlay={deviceMenu} trigger={['click']}>
            <Button>
              {currentDevice.icon}
              <span style={{ marginLeft: 4 }}>{currentDevice.name}</span>
            </Button>
          </Dropdown>

          {/* 尺寸显示 */}
          <Badge count={currentBreakpoint} color="blue">
            <span className="size-display">
              {displaySize.width} × {displaySize.height}
            </span>
          </Badge>

          {/* 方向切换 */}
          <Tooltip title="旋转设备">
            <Button
              icon={<RotateLeftOutlined />}
              onClick={handleOrientationToggle}
              disabled={currentDevice.category === 'desktop'}
            />
          </Tooltip>

          <Divider type="vertical" />

          {/* 缩放控制 */}
          <Space>
            <span>缩放:</span>
            <Slider
              value={zoomLevel}
              min={0.1}
              max={2}
              step={0.1}
              style={{ width: 100 }}
              onChange={setZoomLevel}
            />
            <span>{Math.round(zoomLevel * 100)}%</span>
            <Button size="small" onClick={handleAutoZoom}>
              适应
            </Button>
          </Space>

          <Divider type="vertical" />

          {/* 显示选项 */}
          <Space>
            <Tooltip title="显示设备边框">
              <Switch
                checked={showFrame}
                onChange={setShowFrame}
                size="small"
              />
            </Tooltip>
            <Tooltip title="显示标尺">
              <Switch
                checked={showRulers}
                onChange={setShowRulers}
                size="small"
              />
            </Tooltip>
            <Tooltip title="全屏预览">
              <Button
                icon={isFullscreen ? <CompressOutlined /> : <ExpandOutlined />}
                onClick={handleFullscreenToggle}
              />
            </Tooltip>
            <Tooltip title="刷新预览">
              <Button icon={<ReloadOutlined />} />
            </Tooltip>
          </Space>
        </Space>
      </div>

      {/* 预览区域 */}
      <div className="preview-area">
        {/* 标尺 */}
        {showRulers && (
          <>
            <div className="ruler ruler-horizontal">
              {Array.from({ length: Math.ceil(displaySize.width / 50) }, (_, i) => (
                <div key={i} className="ruler-mark" style={{ left: i * 50 * zoomLevel }}>
                  <span>{i * 50}</span>
                </div>
              ))}
            </div>
            <div className="ruler ruler-vertical">
              {Array.from({ length: Math.ceil(displaySize.height / 50) }, (_, i) => (
                <div key={i} className="ruler-mark" style={{ top: i * 50 * zoomLevel }}>
                  <span>{i * 50}</span>
                </div>
              ))}
            </div>
          </>
        )}

        {/* 设备框架 */}
        <div
          ref={frameRef}
          className={`device-frame ${showFrame ? 'with-frame' : ''} ${currentDevice.category}`}
          style={{
            width: displaySize.width * zoomLevel,
            height: displaySize.height * zoomLevel,
            transform: `scale(1)`,
            transformOrigin: 'top left'
          }}
        >
          {/* 设备内容 */}
          <div
            className="device-content"
            style={{
              width: displaySize.width,
              height: displaySize.height,
              transform: `scale(${zoomLevel})`,
              transformOrigin: 'top left'
            }}
          >
            {children}
          </div>

          {/* 设备信息覆盖层 */}
          <div className="device-info">
            <Space>
              <span>{currentDevice.name}</span>
              <span>{displaySize.width} × {displaySize.height}</span>
              <span>@{currentDevice.pixelRatio}x</span>
              {isLandscape && <span>横屏</span>}
            </Space>
          </div>
        </div>
      </div>

      {/* 断点指示器 */}
      <div className="breakpoint-indicators">
        {Object.entries(BREAKPOINTS).map(([name, width]) => (
          <div
            key={name}
            className={`breakpoint-indicator ${displaySize.width >= width ? 'active' : ''}`}
            style={{ left: width * zoomLevel }}
          >
            <span>{name}</span>
            <span>{width}px</span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ResponsivePreview;
