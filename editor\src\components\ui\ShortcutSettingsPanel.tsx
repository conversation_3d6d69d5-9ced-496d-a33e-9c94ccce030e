/**
 * ShortcutSettingsPanel.tsx
 * 
 * 快捷键设置面板组件
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Modal,
  Table,
  Input,
  Button,
  Switch,
  Tag,
  Space,
  Tabs,
  Typography,
  Tooltip,
  message,
  Popconfirm,
  Badge
} from 'antd';
import {
  SearchOutlined,
  EditOutlined,
  ReloadOutlined,
  ExportOutlined,
  ImportOutlined,
  SettingOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import './ShortcutSettingsPanel.module.css';

const { Search } = Input;
const { Text, Title } = Typography;
const { TabPane } = Tabs;

/**
 * 快捷键数据接口
 */
interface ShortcutData {
  id: string;
  name: string;
  description: string;
  keys: string[];
  category: string;
  enabled: boolean;
  global?: boolean;
  context?: string;
}

/**
 * 快捷键分类接口
 */
interface ShortcutCategory {
  id: string;
  name: string;
  description: string;
  shortcuts: ShortcutData[];
}

/**
 * 快捷键设置面板属性
 */
export interface ShortcutSettingsPanelProps {
  /** 是否显示 */
  visible: boolean;
  /** 关闭回调 */
  onClose: () => void;
  /** 快捷键数据 */
  shortcuts: ShortcutData[];
  /** 分类数据 */
  categories: ShortcutCategory[];
  /** 快捷键变化回调 */
  onShortcutChange?: (shortcut: ShortcutData) => void;
  /** 重置回调 */
  onReset?: () => void;
  /** 导出回调 */
  onExport?: () => void;
  /** 导入回调 */
  onImport?: (data: any) => void;
}

/**
 * 快捷键编辑模态框
 */
const ShortcutEditModal: React.FC<{
  visible: boolean;
  shortcut: ShortcutData | null;
  onOk: (shortcut: ShortcutData) => void;
  onCancel: () => void;
}> = ({ visible, shortcut, onOk, onCancel }) => {
  const [keys, setKeys] = useState<string[]>([]);
  const [isRecording, setIsRecording] = useState(false);

  useEffect(() => {
    if (shortcut) {
      setKeys([...shortcut.keys]);
    }
  }, [shortcut]);

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!isRecording) return;

    event.preventDefault();
    event.stopPropagation();

    const newKeys: string[] = [];
    
    if (event.ctrlKey) newKeys.push('Ctrl');
    if (event.altKey) newKeys.push('Alt');
    if (event.shiftKey) newKeys.push('Shift');
    if (event.metaKey) newKeys.push('Meta');

    if (!['Control', 'Alt', 'Shift', 'Meta'].includes(event.key)) {
      newKeys.push(event.key);
    }

    setKeys(newKeys);
  }, [isRecording]);

  useEffect(() => {
    if (isRecording) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isRecording, handleKeyDown]);

  const handleOk = () => {
    if (shortcut && keys.length > 0) {
      onOk({ ...shortcut, keys });
    }
  };

  const startRecording = () => {
    setIsRecording(true);
    setKeys([]);
  };

  const stopRecording = () => {
    setIsRecording(false);
  };

  return (
    <Modal
      title="编辑快捷键"
      open={visible}
      onOk={handleOk}
      onCancel={onCancel}
      okText="确定"
      cancelText="取消"
      width={500}
    >
      {shortcut && (
        <div className="shortcut-edit-content">
          <div className="shortcut-info">
            <Title level={5}>{shortcut.name}</Title>
            <Text type="secondary">{shortcut.description}</Text>
          </div>
          
          <div className="shortcut-keys">
            <Text strong>当前快捷键:</Text>
            <div className="keys-display">
              {keys.map((key, index) => (
                <Tag key={index} color="blue">{key}</Tag>
              ))}
              {keys.length === 0 && <Text type="secondary">未设置</Text>}
            </div>
          </div>

          <div className="shortcut-record">
            <Space>
              <Button
                type={isRecording ? 'danger' : 'primary'}
                onClick={isRecording ? stopRecording : startRecording}
              >
                {isRecording ? '停止录制' : '录制新快捷键'}
              </Button>
              {isRecording && (
                <Text type="warning">请按下新的快捷键组合...</Text>
              )}
            </Space>
          </div>
        </div>
      )}
    </Modal>
  );
};

/**
 * 快捷键设置面板
 */
export const ShortcutSettingsPanel: React.FC<ShortcutSettingsPanelProps> = ({
  visible,
  onClose,
  shortcuts,
  categories,
  onShortcutChange,
  onReset,
  onExport,
  onImport
}) => {
  const [searchText, setSearchText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [editingShortcut, setEditingShortcut] = useState<ShortcutData | null>(null);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);

  // 过滤快捷键
  const filteredShortcuts = useMemo(() => {
    let filtered = shortcuts;

    // 按分类过滤
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(s => s.category === selectedCategory);
    }

    // 按搜索文本过滤
    if (searchText) {
      const searchLower = searchText.toLowerCase();
      filtered = filtered.filter(s => 
        s.name.toLowerCase().includes(searchLower) ||
        s.description.toLowerCase().includes(searchLower) ||
        s.keys.some(key => key.toLowerCase().includes(searchLower))
      );
    }

    return filtered;
  }, [shortcuts, selectedCategory, searchText]);

  // 表格列定义
  const columns: ColumnsType<ShortcutData> = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      render: (text, record) => (
        <div>
          <Text strong>{text}</Text>
          {record.global && <Badge count="全局" style={{ marginLeft: 8 }} />}
        </div>
      )
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: '快捷键',
      dataIndex: 'keys',
      key: 'keys',
      width: 200,
      render: (keys: string[]) => (
        <Space>
          {keys.map((key, index) => (
            <Tag key={index} color="blue">{key}</Tag>
          ))}
        </Space>
      )
    },
    {
      title: '状态',
      dataIndex: 'enabled',
      key: 'enabled',
      width: 80,
      render: (enabled: boolean, record) => (
        <Switch
          checked={enabled}
          onChange={(checked) => {
            const updatedShortcut = { ...record, enabled: checked };
            onShortcutChange?.(updatedShortcut);
          }}
        />
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: 100,
      render: (_, record) => (
        <Space>
          <Tooltip title="编辑快捷键">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => {
                setEditingShortcut(record);
                setIsEditModalVisible(true);
              }}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  // 处理快捷键编辑
  const handleShortcutEdit = (updatedShortcut: ShortcutData) => {
    onShortcutChange?.(updatedShortcut);
    setIsEditModalVisible(false);
    setEditingShortcut(null);
    message.success('快捷键已更新');
  };

  // 导出配置
  const handleExport = () => {
    const config = {
      shortcuts: shortcuts.map(s => ({
        id: s.id,
        keys: s.keys,
        enabled: s.enabled
      })),
      exportTime: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(config, null, 2)], {
      type: 'application/json'
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'keyboard-shortcuts.json';
    a.click();
    URL.revokeObjectURL(url);

    onExport?.();
    message.success('快捷键配置已导出');
  };

  // 导入配置
  const handleImport = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          try {
            const config = JSON.parse(e.target?.result as string);
            onImport?.(config);
            message.success('快捷键配置已导入');
          } catch (error) {
            message.error('导入失败：文件格式错误');
          }
        };
        reader.readAsText(file);
      }
    };
    input.click();
  };

  // 重置配置
  const handleReset = () => {
    onReset?.();
    message.success('快捷键配置已重置');
  };

  return (
    <>
      <Modal
        title={
          <div className="panel-header">
            <SettingOutlined style={{ marginRight: 8 }} />
            快捷键设置
          </div>
        }
        open={visible}
        onCancel={onClose}
        footer={null}
        width={1000}
        className="shortcut-settings-panel"
      >
        <div className="panel-content">
          {/* 工具栏 */}
          <div className="panel-toolbar">
            <Space>
              <Search
                placeholder="搜索快捷键..."
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                style={{ width: 250 }}
                allowClear
              />
              <Button icon={<ExportOutlined />} onClick={handleExport}>
                导出
              </Button>
              <Button icon={<ImportOutlined />} onClick={handleImport}>
                导入
              </Button>
              <Popconfirm
                title="确定要重置所有快捷键设置吗？"
                onConfirm={handleReset}
                okText="确定"
                cancelText="取消"
              >
                <Button icon={<ReloadOutlined />} danger>
                  重置
                </Button>
              </Popconfirm>
            </Space>
          </div>

          {/* 分类标签页 */}
          <Tabs
            activeKey={selectedCategory}
            onChange={setSelectedCategory}
            type="card"
          >
            <TabPane tab="全部" key="all" />
            {categories.map(category => (
              <TabPane
                tab={`${category.name} (${category.shortcuts.length})`}
                key={category.id}
              />
            ))}
          </Tabs>

          {/* 快捷键表格 */}
          <Table
            columns={columns}
            dataSource={filteredShortcuts}
            rowKey="id"
            pagination={{
              pageSize: 20,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 个快捷键`
            }}
            size="small"
          />
        </div>
      </Modal>

      {/* 快捷键编辑模态框 */}
      <ShortcutEditModal
        visible={isEditModalVisible}
        shortcut={editingShortcut}
        onOk={handleShortcutEdit}
        onCancel={() => {
          setIsEditModalVisible(false);
          setEditingShortcut(null);
        }}
      />
    </>
  );
};

export default ShortcutSettingsPanel;
