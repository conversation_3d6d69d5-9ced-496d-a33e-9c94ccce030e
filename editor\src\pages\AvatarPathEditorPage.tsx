/**
 * 数字人路径编辑器页面
 */
import React, { useState, useEffect, useCallback } from 'react';
import {
  Layout,
  Card,
  Button,
  Space,
  List,
  Modal,
  message,
  Input,
  Select,
  Divider,
  Typography,
  Tag,
  Tooltip,
  Popconfirm,
  Row,
  Col
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  ExportOutlined,
  ImportOutlined,
  SearchOutlined,
  FilterOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../store';
import { AvatarPathEditor, PathEditorUtils } from '../components/AvatarPathEditor';
import ScenePanel from '../components/ScenePanel';

const { Header, Content, Sider } = Layout;
const { Title, Text } = Typography;
const { Search } = Input;
const { Option } = Select;

/**
 * 路径数据接口
 */
interface PathData {
  id: string;
  name: string;
  avatarId: string;
  points: any[];
  loopMode: 'none' | 'loop' | 'pingpong';
  interpolation: 'linear' | 'smooth' | 'bezier' | 'spline';
  enabled: boolean;
  totalDuration: number;
  metadata: {
    createdAt: string;
    updatedAt: string;
    creator: string;
    version: number;
    description?: string;
    tags?: string[];
  };
}

/**
 * 数字人路径编辑器页面
 */
const AvatarPathEditorPage: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  
  // Redux状态
  const selectedEntityId = useSelector((state: RootState) => state.scene.selectedEntityId);
  const entities = useSelector((state: RootState) => state.scene.entities);
  
  // 本地状态
  const [paths, setPaths] = useState<PathData[]>([]);
  const [selectedPathId, setSelectedPathId] = useState<string | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showImportModal, setShowImportModal] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [filterAvatarId, setFilterAvatarId] = useState<string>('');
  const [filterEnabled, setFilterEnabled] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  /**
   * 获取当前选中的路径
   */
  const selectedPath = paths.find(path => path.id === selectedPathId);

  /**
   * 获取可用的数字人列表
   */
  const availableAvatars = entities.filter(entity => 
    entity.components.some(comp => comp.type === 'AvatarComponent')
  );

  /**
   * 过滤路径列表
   */
  const filteredPaths = paths.filter(path => {
    const matchesSearch = !searchText || 
      path.name.toLowerCase().includes(searchText.toLowerCase()) ||
      path.id.toLowerCase().includes(searchText.toLowerCase());
    
    const matchesAvatar = !filterAvatarId || path.avatarId === filterAvatarId;
    
    const matchesEnabled = !filterEnabled || 
      (filterEnabled === 'enabled' && path.enabled) ||
      (filterEnabled === 'disabled' && !path.enabled);

    return matchesSearch && matchesAvatar && matchesEnabled;
  });

  /**
   * 创建新路径
   */
  const handleCreatePath = useCallback((values: { name: string; avatarId: string }) => {
    const newPath = PathEditorUtils.createDefaultPath(values.name, values.avatarId);
    setPaths(prev => [...prev, newPath]);
    setSelectedPathId(newPath.id);
    setShowCreateModal(false);
    message.success(t('avatarPathEditor.pathCreated'));
  }, [t]);

  /**
   * 删除路径
   */
  const handleDeletePath = useCallback((pathId: string) => {
    setPaths(prev => prev.filter(path => path.id !== pathId));
    if (selectedPathId === pathId) {
      setSelectedPathId(null);
    }
    message.success(t('avatarPathEditor.pathDeleted'));
  }, [selectedPathId, t]);

  /**
   * 复制路径
   */
  const handleCopyPath = useCallback((pathId: string) => {
    const originalPath = paths.find(path => path.id === pathId);
    if (!originalPath) return;

    const copiedPath = {
      ...originalPath,
      id: `path_${Date.now()}`,
      name: `${originalPath.name} (副本)`,
      metadata: {
        ...originalPath.metadata,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        version: 1
      }
    };

    setPaths(prev => [...prev, copiedPath]);
    message.success(t('avatarPathEditor.pathCopied'));
  }, [paths, t]);

  /**
   * 路径变化处理
   */
  const handlePathChange = useCallback((updatedPath: PathData) => {
    setPaths(prev => prev.map(path => 
      path.id === updatedPath.id ? updatedPath : path
    ));
  }, []);

  /**
   * 保存路径
   */
  const handleSavePath = useCallback(async (path: PathData) => {
    setIsLoading(true);
    try {
      // 这里应该调用实际的保存API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setPaths(prev => prev.map(p => 
        p.id === path.id ? {
          ...path,
          metadata: {
            ...path.metadata,
            updatedAt: new Date().toISOString(),
            version: path.metadata.version + 1
          }
        } : p
      ));
      
      message.success(t('avatarPathEditor.pathSaved'));
    } catch (error) {
      message.error(t('avatarPathEditor.saveError'));
    } finally {
      setIsLoading(false);
    }
  }, [t]);

  /**
   * 导出路径
   */
  const handleExportPath = useCallback((pathId: string) => {
    const path = paths.find(p => p.id === pathId);
    if (!path) return;

    const jsonString = PathEditorUtils.exportPathToJSON(path);
    const blob = new Blob([jsonString], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `${path.name}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    message.success(t('avatarPathEditor.pathExported'));
  }, [paths, t]);

  /**
   * 导入路径
   */
  const handleImportPath = useCallback((file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const jsonString = e.target?.result as string;
        const importedPath = PathEditorUtils.importPathFromJSON(jsonString);
        
        // 生成新的ID避免冲突
        importedPath.id = `path_${Date.now()}`;
        importedPath.metadata.createdAt = new Date().toISOString();
        importedPath.metadata.updatedAt = new Date().toISOString();
        
        setPaths(prev => [...prev, importedPath]);
        setSelectedPathId(importedPath.id);
        message.success(t('avatarPathEditor.pathImported'));
      } catch (error) {
        message.error(t('avatarPathEditor.importError'));
      }
    };
    reader.readAsText(file);
  }, [t]);

  /**
   * 刷新路径列表
   */
  const handleRefreshPaths = useCallback(async () => {
    setIsLoading(true);
    try {
      // 这里应该调用实际的API获取路径列表
      await new Promise(resolve => setTimeout(resolve, 1000));
      message.success(t('avatarPathEditor.pathsRefreshed'));
    } catch (error) {
      message.error(t('avatarPathEditor.refreshError'));
    } finally {
      setIsLoading(false);
    }
  }, [t]);

  /**
   * 渲染路径列表项
   */
  const renderPathItem = (path: PathData) => (
    <List.Item
      key={path.id}
      actions={[
        <Tooltip title={t('avatarPathEditor.edit')}>
          <Button
            type="text"
            size="small"
            icon={<EditOutlined />}
            onClick={() => setSelectedPathId(path.id)}
          />
        </Tooltip>,
        <Tooltip title={t('avatarPathEditor.copy')}>
          <Button
            type="text"
            size="small"
            icon={<CopyOutlined />}
            onClick={() => handleCopyPath(path.id)}
          />
        </Tooltip>,
        <Tooltip title={t('avatarPathEditor.export')}>
          <Button
            type="text"
            size="small"
            icon={<ExportOutlined />}
            onClick={() => handleExportPath(path.id)}
          />
        </Tooltip>,
        <Popconfirm
          title={t('avatarPathEditor.confirmDelete')}
          onConfirm={() => handleDeletePath(path.id)}
        >
          <Tooltip title={t('avatarPathEditor.delete')}>
            <Button
              type="text"
              size="small"
              danger
              icon={<DeleteOutlined />}
            />
          </Tooltip>
        </Popconfirm>
      ]}
    >
      <List.Item.Meta
        title={
          <Space>
            <span>{path.name}</span>
            <Tag color={path.enabled ? 'green' : 'red'}>
              {path.enabled ? t('avatarPathEditor.enabled') : t('avatarPathEditor.disabled')}
            </Tag>
            <Tag color="blue">{path.loopMode}</Tag>
            <Tag color="purple">{path.interpolation}</Tag>
          </Space>
        }
        description={
          <Space direction="vertical" size="small">
            <Text type="secondary">
              {t('avatarPathEditor.avatar')}: {path.avatarId}
            </Text>
            <Text type="secondary">
              {t('avatarPathEditor.points')}: {path.points.length} | 
              {t('avatarPathEditor.duration')}: {path.totalDuration.toFixed(1)}s
            </Text>
            <Text type="secondary">
              {t('avatarPathEditor.updated')}: {new Date(path.metadata.updatedAt).toLocaleString()}
            </Text>
          </Space>
        }
      />
    </List.Item>
  );

  return (
    <Layout style={{ height: '100vh' }}>
      {/* 左侧场景面板 */}
      <Sider width={300} theme="light" style={{ borderRight: '1px solid #f0f0f0' }}>
        <ScenePanel />
      </Sider>

      {/* 中间路径列表 */}
      <Sider width={400} theme="light" style={{ borderRight: '1px solid #f0f0f0' }}>
        <div style={{ padding: '16px' }}>
          <Card
            title={t('avatarPathEditor.pathList')}
            extra={
              <Space>
                <Tooltip title={t('avatarPathEditor.refresh')}>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={handleRefreshPaths}
                    loading={isLoading}
                  />
                </Tooltip>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => setShowCreateModal(true)}
                >
                  {t('avatarPathEditor.create')}
                </Button>
              </Space>
            }
            size="small"
          >
            {/* 搜索和过滤 */}
            <Space direction="vertical" style={{ width: '100%', marginBottom: 16 }}>
              <Search
                placeholder={t('avatarPathEditor.searchPlaceholder')}
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                allowClear
              />
              
              <Row gutter={8}>
                <Col span={12}>
                  <Select
                    placeholder={t('avatarPathEditor.filterByAvatar')}
                    value={filterAvatarId}
                    onChange={setFilterAvatarId}
                    allowClear
                    style={{ width: '100%' }}
                  >
                    {availableAvatars.map(avatar => (
                      <Option key={avatar.id} value={avatar.id}>
                        {avatar.name}
                      </Option>
                    ))}
                  </Select>
                </Col>
                <Col span={12}>
                  <Select
                    placeholder={t('avatarPathEditor.filterByStatus')}
                    value={filterEnabled}
                    onChange={setFilterEnabled}
                    allowClear
                    style={{ width: '100%' }}
                  >
                    <Option value="enabled">{t('avatarPathEditor.enabled')}</Option>
                    <Option value="disabled">{t('avatarPathEditor.disabled')}</Option>
                  </Select>
                </Col>
              </Row>
            </Space>

            {/* 路径列表 */}
            <List
              size="small"
              dataSource={filteredPaths}
              renderItem={renderPathItem}
              style={{ maxHeight: 'calc(100vh - 300px)', overflow: 'auto' }}
            />
          </Card>
        </div>
      </Sider>

      {/* 右侧编辑器 */}
      <Layout>
        <Content style={{ padding: '16px', overflow: 'auto' }}>
          {selectedPath ? (
            <AvatarPathEditor
              entityId={selectedEntityId}
              initialPath={selectedPath}
              onPathChange={handlePathChange}
              onPathSave={handleSavePath}
              onPathDelete={handleDeletePath}
            />
          ) : (
            <Card style={{ textAlign: 'center', marginTop: '100px' }}>
              <Title level={4}>{t('avatarPathEditor.selectPath')}</Title>
              <Text type="secondary">
                {t('avatarPathEditor.selectPathDescription')}
              </Text>
              <div style={{ marginTop: 24 }}>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => setShowCreateModal(true)}
                >
                  {t('avatarPathEditor.createFirst')}
                </Button>
              </div>
            </Card>
          )}
        </Content>
      </Layout>

      {/* 创建路径模态框 */}
      <Modal
        title={t('avatarPathEditor.createPath')}
        open={showCreateModal}
        onCancel={() => setShowCreateModal(false)}
        footer={null}
      >
        <CreatePathForm
          availableAvatars={availableAvatars}
          onSubmit={handleCreatePath}
          onCancel={() => setShowCreateModal(false)}
        />
      </Modal>
    </Layout>
  );
};

/**
 * 创建路径表单组件
 */
const CreatePathForm: React.FC<{
  availableAvatars: any[];
  onSubmit: (values: { name: string; avatarId: string }) => void;
  onCancel: () => void;
}> = ({ availableAvatars, onSubmit, onCancel }) => {
  const { t } = useTranslation();
  const [name, setName] = useState('');
  const [avatarId, setAvatarId] = useState('');

  const handleSubmit = () => {
    if (!name.trim()) {
      message.error(t('avatarPathEditor.nameRequired'));
      return;
    }
    if (!avatarId) {
      message.error(t('avatarPathEditor.avatarRequired'));
      return;
    }

    onSubmit({ name: name.trim(), avatarId });
  };

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <div>
        <Text>{t('avatarPathEditor.pathName')}</Text>
        <Input
          value={name}
          onChange={(e) => setName(e.target.value)}
          placeholder={t('avatarPathEditor.pathNamePlaceholder')}
          style={{ marginTop: 8 }}
        />
      </div>

      <div>
        <Text>{t('avatarPathEditor.selectAvatar')}</Text>
        <Select
          value={avatarId}
          onChange={setAvatarId}
          placeholder={t('avatarPathEditor.selectAvatarPlaceholder')}
          style={{ width: '100%', marginTop: 8 }}
        >
          {availableAvatars.map(avatar => (
            <Option key={avatar.id} value={avatar.id}>
              {avatar.name}
            </Option>
          ))}
        </Select>
      </div>

      <div style={{ textAlign: 'right', marginTop: 24 }}>
        <Space>
          <Button onClick={onCancel}>
            {t('avatarPathEditor.cancel')}
          </Button>
          <Button type="primary" onClick={handleSubmit}>
            {t('avatarPathEditor.create')}
          </Button>
        </Space>
      </div>
    </Space>
  );
};

export default AvatarPathEditorPage;
