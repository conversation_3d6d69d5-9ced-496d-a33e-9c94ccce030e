/**
 * 国际化Hook - 提供多语言支持
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { message } from 'antd';

// 支持的语言
export type SupportedLocale = 'zh-CN' | 'en-US' | 'ja-JP' | 'ko-KR' | 'es-ES' | 'fr-FR' | 'de-DE' | 'ru-RU';

// 语言配置
export interface LocaleConfig {
  code: SupportedLocale;
  name: string;
  nativeName: string;
  flag: string;
  rtl: boolean;
}

// 翻译函数类型
export type TranslationFunction = (key: string, params?: Record<string, any>) => string;

// 支持的语言列表
export const SUPPORTED_LOCALES: LocaleConfig[] = [
  {
    code: 'zh-CN',
    name: 'Chinese (Simplified)',
    nativeName: '简体中文',
    flag: '🇨🇳',
    rtl: false,
  },
  {
    code: 'en-US',
    name: 'English (US)',
    nativeName: 'English',
    flag: '🇺🇸',
    rtl: false,
  },
  {
    code: 'ja-<PERSON>',
    name: 'Japanese',
    nativeName: '日本語',
    flag: '🇯🇵',
    rtl: false,
  },
  {
    code: 'ko-KR',
    name: 'Korean',
    nativeName: '한국어',
    flag: '🇰🇷',
    rtl: false,
  },
  {
    code: 'es-ES',
    name: 'Spanish',
    nativeName: 'Español',
    flag: '🇪🇸',
    rtl: false,
  },
  {
    code: 'fr-FR',
    name: 'French',
    nativeName: 'Français',
    flag: '🇫🇷',
    rtl: false,
  },
  {
    code: 'de-DE',
    name: 'German',
    nativeName: 'Deutsch',
    flag: '🇩🇪',
    rtl: false,
  },
  {
    code: 'ru-RU',
    name: 'Russian',
    nativeName: 'Русский',
    flag: '🇷🇺',
    rtl: false,
  },
];

// 翻译缓存
const translationCache = new Map<string, Record<string, any>>();

// 默认语言
const DEFAULT_LOCALE: SupportedLocale = 'en-US';

// 从localStorage获取保存的语言设置
const getStoredLocale = (): SupportedLocale => {
  try {
    const stored = localStorage.getItem('dl-engine-locale');
    if (stored && SUPPORTED_LOCALES.some(locale => locale.code === stored)) {
      return stored as SupportedLocale;
    }
  } catch (error) {
    console.warn('Failed to get stored locale:', error);
  }
  return DEFAULT_LOCALE;
};

// 检测浏览器语言
const detectBrowserLocale = (): SupportedLocale => {
  const browserLang = navigator.language || navigator.languages?.[0];
  
  if (browserLang) {
    // 精确匹配
    const exactMatch = SUPPORTED_LOCALES.find(locale => locale.code === browserLang);
    if (exactMatch) {
      return exactMatch.code;
    }
    
    // 语言代码匹配（忽略地区）
    const langCode = browserLang.split('-')[0];
    const langMatch = SUPPORTED_LOCALES.find(locale => locale.code.startsWith(langCode));
    if (langMatch) {
      return langMatch.code;
    }
  }
  
  return DEFAULT_LOCALE;
};

// 加载翻译文件
const loadTranslations = async (locale: SupportedLocale): Promise<Record<string, any>> => {
  const cacheKey = locale;
  
  // 检查缓存
  if (translationCache.has(cacheKey)) {
    return translationCache.get(cacheKey)!;
  }
  
  try {
    // 动态导入翻译文件
    const translations = await import(`../locales/${locale}/blockchain.json`);
    const data = translations.default || translations;
    
    // 缓存翻译数据
    translationCache.set(cacheKey, data);
    
    return data;
  } catch (error) {
    console.error(`Failed to load translations for ${locale}:`, error);
    
    // 如果加载失败，尝试加载默认语言
    if (locale !== DEFAULT_LOCALE) {
      return loadTranslations(DEFAULT_LOCALE);
    }
    
    // 返回空对象作为后备
    return {};
  }
};

// 获取嵌套对象的值
const getNestedValue = (obj: any, path: string): any => {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);
};

// 替换参数
const replaceParams = (text: string, params: Record<string, any>): string => {
  return text.replace(/\{\{(\w+)\}\}/g, (match, key) => {
    return params[key] !== undefined ? String(params[key]) : match;
  });
};

export interface UseI18nReturn {
  locale: SupportedLocale;
  localeConfig: LocaleConfig;
  isLoading: boolean;
  error: string | null;
  t: TranslationFunction;
  setLocale: (locale: SupportedLocale) => Promise<void>;
  getSupportedLocales: () => LocaleConfig[];
  isRTL: boolean;
}

export const useI18n = (): UseI18nReturn => {
  const [locale, setLocaleState] = useState<SupportedLocale>(() => {
    // 优先使用存储的语言，其次是浏览器检测的语言
    const stored = getStoredLocale();
    return stored !== DEFAULT_LOCALE ? stored : detectBrowserLocale();
  });
  
  const [translations, setTranslations] = useState<Record<string, any>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 获取当前语言配置
  const localeConfig = useMemo(() => {
    return SUPPORTED_LOCALES.find(l => l.code === locale) || SUPPORTED_LOCALES[0];
  }, [locale]);

  // 是否为RTL语言
  const isRTL = useMemo(() => {
    return localeConfig.rtl;
  }, [localeConfig]);

  // 翻译函数
  const t: TranslationFunction = useCallback((key: string, params?: Record<string, any>) => {
    const value = getNestedValue(translations, key);
    
    if (value === undefined) {
      console.warn(`Translation missing for key: ${key} in locale: ${locale}`);
      return key; // 返回key作为后备
    }
    
    if (typeof value !== 'string') {
      console.warn(`Translation value is not a string for key: ${key}`);
      return key;
    }
    
    // 如果有参数，进行替换
    if (params) {
      return replaceParams(value, params);
    }
    
    return value;
  }, [translations, locale]);

  // 设置语言
  const setLocale = useCallback(async (newLocale: SupportedLocale) => {
    if (newLocale === locale) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // 加载新语言的翻译
      const newTranslations = await loadTranslations(newLocale);
      
      // 更新状态
      setLocaleState(newLocale);
      setTranslations(newTranslations);
      
      // 保存到localStorage
      try {
        localStorage.setItem('dl-engine-locale', newLocale);
      } catch (error) {
        console.warn('Failed to save locale to localStorage:', error);
      }
      
      // 更新HTML lang属性
      document.documentElement.lang = newLocale;
      
      // 更新HTML dir属性（RTL支持）
      const newLocaleConfig = SUPPORTED_LOCALES.find(l => l.code === newLocale);
      document.documentElement.dir = newLocaleConfig?.rtl ? 'rtl' : 'ltr';
      
      message.success(t('common.languageChanged', { language: newLocaleConfig?.nativeName }));
    } catch (error) {
      console.error('Failed to set locale:', error);
      setError('Failed to load language');
      message.error('Failed to change language');
    } finally {
      setIsLoading(false);
    }
  }, [locale, t]);

  // 获取支持的语言列表
  const getSupportedLocales = useCallback(() => {
    return SUPPORTED_LOCALES;
  }, []);

  // 初始化加载翻译
  useEffect(() => {
    const initializeTranslations = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const initialTranslations = await loadTranslations(locale);
        setTranslations(initialTranslations);
        
        // 设置HTML属性
        document.documentElement.lang = locale;
        document.documentElement.dir = localeConfig.rtl ? 'rtl' : 'ltr';
      } catch (error) {
        console.error('Failed to initialize translations:', error);
        setError('Failed to load translations');
      } finally {
        setIsLoading(false);
      }
    };

    initializeTranslations();
  }, [locale, localeConfig.rtl]);

  // 监听系统语言变化
  useEffect(() => {
    const handleLanguageChange = () => {
      const newBrowserLocale = detectBrowserLocale();
      if (newBrowserLocale !== locale && getStoredLocale() === DEFAULT_LOCALE) {
        // 只有在没有手动设置语言时才自动切换
        setLocale(newBrowserLocale);
      }
    };

    window.addEventListener('languagechange', handleLanguageChange);
    return () => {
      window.removeEventListener('languagechange', handleLanguageChange);
    };
  }, [locale, setLocale]);

  return {
    locale,
    localeConfig,
    isLoading,
    error,
    t,
    setLocale,
    getSupportedLocales,
    isRTL,
  };
};

// 语言切换器组件的辅助函数
export const formatLocaleOption = (locale: LocaleConfig) => ({
  value: locale.code,
  label: (
    <span style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
      <span>{locale.flag}</span>
      <span>{locale.nativeName}</span>
    </span>
  ),
});

// 预加载翻译文件
export const preloadTranslations = async (locales: SupportedLocale[]) => {
  const promises = locales.map(locale => loadTranslations(locale));
  await Promise.allSettled(promises);
};

// 清除翻译缓存
export const clearTranslationCache = () => {
  translationCache.clear();
};

// 获取翻译进度（用于开发）
export const getTranslationProgress = (locale: SupportedLocale): Promise<{
  total: number;
  translated: number;
  percentage: number;
  missing: string[];
}> => {
  return new Promise(async (resolve) => {
    try {
      const [baseTranslations, targetTranslations] = await Promise.all([
        loadTranslations(DEFAULT_LOCALE),
        loadTranslations(locale),
      ]);

      const getAllKeys = (obj: any, prefix = ''): string[] => {
        const keys: string[] = [];
        for (const key in obj) {
          const fullKey = prefix ? `${prefix}.${key}` : key;
          if (typeof obj[key] === 'object' && obj[key] !== null) {
            keys.push(...getAllKeys(obj[key], fullKey));
          } else {
            keys.push(fullKey);
          }
        }
        return keys;
      };

      const baseKeys = getAllKeys(baseTranslations);
      const missing: string[] = [];
      let translated = 0;

      for (const key of baseKeys) {
        const value = getNestedValue(targetTranslations, key);
        if (value !== undefined) {
          translated++;
        } else {
          missing.push(key);
        }
      }

      resolve({
        total: baseKeys.length,
        translated,
        percentage: (translated / baseKeys.length) * 100,
        missing,
      });
    } catch (error) {
      console.error('Failed to get translation progress:', error);
      resolve({
        total: 0,
        translated: 0,
        percentage: 0,
        missing: [],
      });
    }
  });
};
