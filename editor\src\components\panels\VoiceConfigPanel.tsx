/**
 * 语音配置面板
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Select,
  Slider,
  Button,
  Space,
  Row,
  Col,
  Typography,
  Divider,
  Switch,
  InputNumber,
  Progress,
  Tag,
  message,
  Tabs,
  Upload,
  List,
  Avatar,
} from 'antd';
import {
  SoundOutlined,
  PlayCircleOutlined,
  StopOutlined,
  UploadOutlined,
  SettingOutlined,
  MicrophoneOutlined,
  AudioOutlined,
  ExperimentOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Title, Text } = Typography;
const { Option } = Select;

/**
 * 语音配置接口
 */
interface VoiceConfig {
  // 语音识别配置
  recognition: {
    provider: 'azure' | 'google' | 'baidu' | 'openai';
    language: string;
    enableContinuous: boolean;
    enableInterimResults: boolean;
    enableWordTimestamps: boolean;
    enableSpeakerDiarization: boolean;
    noiseReduction: number;
    sensitivity: number;
  };
  
  // 语音合成配置
  synthesis: {
    provider: 'azure' | 'google' | 'baidu' | 'openai';
    voice: string;
    language: string;
    rate: number;
    pitch: number;
    volume: number;
    style: string;
    emotion: string;
    enableSSML: boolean;
  };
  
  // 音频处理配置
  audioProcessing: {
    inputFormat: string;
    outputFormat: string;
    sampleRate: number;
    channels: number;
    bitRate: number;
    enableNormalization: boolean;
    enableNoiseReduction: boolean;
    enableEchoCancellation: boolean;
    enableAutoGainControl: boolean;
  };
  
  // 嘴形同步配置
  lipSync: {
    method: 'phoneme' | 'audio' | 'hybrid';
    language: string;
    frameRate: number;
    smoothing: number;
    intensity: number;
    enableFormantAnalysis: boolean;
    enablePitchAnalysis: boolean;
  };
}

/**
 * 语音测试结果
 */
interface VoiceTestResult {
  type: 'recognition' | 'synthesis' | 'lipSync';
  success: boolean;
  duration: number;
  quality: number;
  details: any;
}

/**
 * 语音配置面板组件
 */
const VoiceConfigPanel: React.FC = () => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [config, setConfig] = useState<VoiceConfig | null>(null);
  const [loading, setLoading] = useState(false);
  const [testing, setTesting] = useState(false);
  const [testResults, setTestResults] = useState<VoiceTestResult[]>([]);
  const [previewPlaying, setPreviewPlaying] = useState(false);
  const [recording, setRecording] = useState(false);

  /**
   * 加载语音配置
   */
  const loadVoiceConfig = async () => {
    setLoading(true);
    try {
      // 这里调用API获取语音配置
      const mockConfig: VoiceConfig = {
        recognition: {
          provider: 'azure',
          language: 'zh-CN',
          enableContinuous: true,
          enableInterimResults: true,
          enableWordTimestamps: true,
          enableSpeakerDiarization: false,
          noiseReduction: 0.7,
          sensitivity: 0.8,
        },
        synthesis: {
          provider: 'azure',
          voice: 'zh-CN-XiaoxiaoNeural',
          language: 'zh-CN',
          rate: 1.0,
          pitch: 1.0,
          volume: 1.0,
          style: 'friendly',
          emotion: 'calm',
          enableSSML: true,
        },
        audioProcessing: {
          inputFormat: 'webm',
          outputFormat: 'wav',
          sampleRate: 16000,
          channels: 1,
          bitRate: 128,
          enableNormalization: true,
          enableNoiseReduction: true,
          enableEchoCancellation: true,
          enableAutoGainControl: true,
        },
        lipSync: {
          method: 'hybrid',
          language: 'zh-CN',
          frameRate: 30,
          smoothing: 0.5,
          intensity: 1.0,
          enableFormantAnalysis: true,
          enablePitchAnalysis: true,
        },
      };
      setConfig(mockConfig);
      form.setFieldsValue(mockConfig);
    } catch (error) {
      message.error('加载语音配置失败');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 保存配置
   */
  const handleSaveConfig = async (values: any) => {
    try {
      // 这里调用API保存配置
      console.log('保存语音配置:', values);
      message.success('配置保存成功');
      setConfig(values);
    } catch (error) {
      message.error('保存配置失败');
    }
  };

  /**
   * 语音识别测试
   */
  const handleTestRecognition = async () => {
    setTesting(true);
    setRecording(true);
    
    try {
      // 模拟语音识别测试
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      const result: VoiceTestResult = {
        type: 'recognition',
        success: true,
        duration: 3000,
        quality: 0.92,
        details: {
          text: '这是语音识别测试结果',
          confidence: 0.92,
          words: [
            { word: '这是', confidence: 0.95 },
            { word: '语音', confidence: 0.90 },
            { word: '识别', confidence: 0.88 },
            { word: '测试', confidence: 0.94 },
            { word: '结果', confidence: 0.91 },
          ],
        },
      };
      
      setTestResults(prev => [...prev, result]);
      message.success('语音识别测试完成');
      
    } catch (error) {
      message.error('语音识别测试失败');
    } finally {
      setTesting(false);
      setRecording(false);
    }
  };

  /**
   * 语音合成测试
   */
  const handleTestSynthesis = async () => {
    setTesting(true);
    setPreviewPlaying(true);
    
    try {
      // 模拟语音合成测试
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const result: VoiceTestResult = {
        type: 'synthesis',
        success: true,
        duration: 2000,
        quality: 0.95,
        details: {
          text: '这是语音合成测试',
          audioLength: 2.5,
          sampleRate: 16000,
          format: 'wav',
        },
      };
      
      setTestResults(prev => [...prev, result]);
      message.success('语音合成测试完成');
      
    } catch (error) {
      message.error('语音合成测试失败');
    } finally {
      setTesting(false);
      setPreviewPlaying(false);
    }
  };

  /**
   * 嘴形同步测试
   */
  const handleTestLipSync = async () => {
    setTesting(true);
    
    try {
      // 模拟嘴形同步测试
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const result: VoiceTestResult = {
        type: 'lipSync',
        success: true,
        duration: 1500,
        quality: 0.88,
        details: {
          keyframes: 45,
          accuracy: 0.88,
          method: 'hybrid',
          frameRate: 30,
        },
      };
      
      setTestResults(prev => [...prev, result]);
      message.success('嘴形同步测试完成');
      
    } catch (error) {
      message.error('嘴形同步测试失败');
    } finally {
      setTesting(false);
    }
  };

  /**
   * 清空测试结果
   */
  const handleClearResults = () => {
    setTestResults([]);
  };

  /**
   * 获取可用语音列表
   */
  const getAvailableVoices = (provider: string, language: string) => {
    const voices: Record<string, Record<string, string[]>> = {
      azure: {
        'zh-CN': ['zh-CN-XiaoxiaoNeural', 'zh-CN-YunxiNeural', 'zh-CN-YunyangNeural'],
        'en-US': ['en-US-JennyNeural', 'en-US-GuyNeural', 'en-US-AriaNeural'],
      },
      google: {
        'zh-CN': ['zh-CN-Wavenet-A', 'zh-CN-Wavenet-B', 'zh-CN-Wavenet-C'],
        'en-US': ['en-US-Wavenet-A', 'en-US-Wavenet-B', 'en-US-Wavenet-C'],
      },
    };
    
    return voices[provider]?.[language] || [];
  };

  useEffect(() => {
    loadVoiceConfig();
  }, []);

  if (!config) {
    return <div>加载中...</div>;
  }

  return (
    <div style={{ padding: '16px', height: '100%', overflow: 'auto' }}>
      <div style={{ marginBottom: '16px' }}>
        <Title level={4}>语音配置</Title>
        <Space>
          <Button type="primary" onClick={() => form.submit()}>
            保存配置
          </Button>
          <Button icon={<ExperimentOutlined />} onClick={handleClearResults}>
            清空测试
          </Button>
        </Space>
      </div>

      <Row gutter={16}>
        <Col span={16}>
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSaveConfig}
            initialValues={config}
          >
            <Tabs
              items={[
                {
                  key: 'recognition',
                  label: '语音识别',
                  icon: <MicrophoneOutlined />,
                  children: (
                    <Card>
                      <Row gutter={16}>
                        <Col span={12}>
                          <Form.Item name={['recognition', 'provider']} label="服务提供商">
                            <Select>
                              <Option value="azure">Azure Speech</Option>
                              <Option value="google">Google Cloud</Option>
                              <Option value="baidu">百度语音</Option>
                              <Option value="openai">OpenAI Whisper</Option>
                            </Select>
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item name={['recognition', 'language']} label="识别语言">
                            <Select>
                              <Option value="zh-CN">中文(简体)</Option>
                              <Option value="zh-TW">中文(繁体)</Option>
                              <Option value="en-US">英语(美国)</Option>
                              <Option value="ja-JP">日语</Option>
                            </Select>
                          </Form.Item>
                        </Col>
                      </Row>
                      
                      <Row gutter={16}>
                        <Col span={8}>
                          <Form.Item name={['recognition', 'enableContinuous']} label="连续识别" valuePropName="checked">
                            <Switch />
                          </Form.Item>
                        </Col>
                        <Col span={8}>
                          <Form.Item name={['recognition', 'enableInterimResults']} label="中间结果" valuePropName="checked">
                            <Switch />
                          </Form.Item>
                        </Col>
                        <Col span={8}>
                          <Form.Item name={['recognition', 'enableWordTimestamps']} label="词时间戳" valuePropName="checked">
                            <Switch />
                          </Form.Item>
                        </Col>
                      </Row>
                      
                      <Row gutter={16}>
                        <Col span={12}>
                          <Form.Item name={['recognition', 'noiseReduction']} label="降噪强度">
                            <Slider min={0} max={1} step={0.1} marks={{ 0: '关闭', 0.5: '中等', 1: '最强' }} />
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item name={['recognition', 'sensitivity']} label="灵敏度">
                            <Slider min={0} max={1} step={0.1} marks={{ 0: '低', 0.5: '中', 1: '高' }} />
                          </Form.Item>
                        </Col>
                      </Row>
                      
                      <Button
                        type="primary"
                        icon={recording ? <StopOutlined /> : <MicrophoneOutlined />}
                        onClick={handleTestRecognition}
                        loading={testing && recording}
                        danger={recording}
                      >
                        {recording ? '停止录音' : '测试识别'}
                      </Button>
                    </Card>
                  ),
                },
                {
                  key: 'synthesis',
                  label: '语音合成',
                  icon: <AudioOutlined />,
                  children: (
                    <Card>
                      <Row gutter={16}>
                        <Col span={12}>
                          <Form.Item name={['synthesis', 'provider']} label="服务提供商">
                            <Select>
                              <Option value="azure">Azure Speech</Option>
                              <Option value="google">Google Cloud</Option>
                              <Option value="baidu">百度语音</Option>
                              <Option value="openai">OpenAI TTS</Option>
                            </Select>
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item name={['synthesis', 'voice']} label="语音角色">
                            <Select>
                              {getAvailableVoices(config.synthesis.provider, config.synthesis.language).map(voice => (
                                <Option key={voice} value={voice}>{voice}</Option>
                              ))}
                            </Select>
                          </Form.Item>
                        </Col>
                      </Row>
                      
                      <Row gutter={16}>
                        <Col span={8}>
                          <Form.Item name={['synthesis', 'rate']} label="语速">
                            <Slider min={0.5} max={2.0} step={0.1} marks={{ 0.5: '慢', 1: '正常', 2: '快' }} />
                          </Form.Item>
                        </Col>
                        <Col span={8}>
                          <Form.Item name={['synthesis', 'pitch']} label="音调">
                            <Slider min={0.5} max={2.0} step={0.1} marks={{ 0.5: '低', 1: '正常', 2: '高' }} />
                          </Form.Item>
                        </Col>
                        <Col span={8}>
                          <Form.Item name={['synthesis', 'volume']} label="音量">
                            <Slider min={0.1} max={1.0} step={0.1} marks={{ 0.1: '小', 0.5: '中', 1: '大' }} />
                          </Form.Item>
                        </Col>
                      </Row>
                      
                      <Row gutter={16}>
                        <Col span={12}>
                          <Form.Item name={['synthesis', 'style']} label="语音风格">
                            <Select>
                              <Option value="friendly">友好</Option>
                              <Option value="professional">专业</Option>
                              <Option value="warm">温暖</Option>
                              <Option value="calm">平静</Option>
                            </Select>
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item name={['synthesis', 'emotion']} label="情感">
                            <Select>
                              <Option value="neutral">中性</Option>
                              <Option value="happy">开心</Option>
                              <Option value="calm">平静</Option>
                              <Option value="gentle">温和</Option>
                            </Select>
                          </Form.Item>
                        </Col>
                      </Row>
                      
                      <Button
                        type="primary"
                        icon={previewPlaying ? <StopOutlined /> : <PlayCircleOutlined />}
                        onClick={handleTestSynthesis}
                        loading={testing && previewPlaying}
                      >
                        {previewPlaying ? '停止播放' : '测试合成'}
                      </Button>
                    </Card>
                  ),
                },
                {
                  key: 'audioProcessing',
                  label: '音频处理',
                  icon: <SettingOutlined />,
                  children: (
                    <Card>
                      <Row gutter={16}>
                        <Col span={8}>
                          <Form.Item name={['audioProcessing', 'sampleRate']} label="采样率">
                            <Select>
                              <Option value={8000}>8kHz</Option>
                              <Option value={16000}>16kHz</Option>
                              <Option value={22050}>22kHz</Option>
                              <Option value={44100}>44kHz</Option>
                            </Select>
                          </Form.Item>
                        </Col>
                        <Col span={8}>
                          <Form.Item name={['audioProcessing', 'channels']} label="声道数">
                            <Select>
                              <Option value={1}>单声道</Option>
                              <Option value={2}>立体声</Option>
                            </Select>
                          </Form.Item>
                        </Col>
                        <Col span={8}>
                          <Form.Item name={['audioProcessing', 'bitRate']} label="比特率">
                            <Select>
                              <Option value={64}>64 kbps</Option>
                              <Option value={128}>128 kbps</Option>
                              <Option value={192}>192 kbps</Option>
                              <Option value={320}>320 kbps</Option>
                            </Select>
                          </Form.Item>
                        </Col>
                      </Row>
                      
                      <Row gutter={16}>
                        <Col span={6}>
                          <Form.Item name={['audioProcessing', 'enableNormalization']} label="音频标准化" valuePropName="checked">
                            <Switch />
                          </Form.Item>
                        </Col>
                        <Col span={6}>
                          <Form.Item name={['audioProcessing', 'enableNoiseReduction']} label="降噪处理" valuePropName="checked">
                            <Switch />
                          </Form.Item>
                        </Col>
                        <Col span={6}>
                          <Form.Item name={['audioProcessing', 'enableEchoCancellation']} label="回声消除" valuePropName="checked">
                            <Switch />
                          </Form.Item>
                        </Col>
                        <Col span={6}>
                          <Form.Item name={['audioProcessing', 'enableAutoGainControl']} label="自动增益" valuePropName="checked">
                            <Switch />
                          </Form.Item>
                        </Col>
                      </Row>
                    </Card>
                  ),
                },
                {
                  key: 'lipSync',
                  label: '嘴形同步',
                  icon: <SoundOutlined />,
                  children: (
                    <Card>
                      <Row gutter={16}>
                        <Col span={12}>
                          <Form.Item name={['lipSync', 'method']} label="同步方法">
                            <Select>
                              <Option value="phoneme">基于音素</Option>
                              <Option value="audio">基于音频</Option>
                              <Option value="hybrid">混合方法</Option>
                            </Select>
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item name={['lipSync', 'frameRate']} label="帧率">
                            <InputNumber min={15} max={60} style={{ width: '100%' }} />
                          </Form.Item>
                        </Col>
                      </Row>
                      
                      <Row gutter={16}>
                        <Col span={12}>
                          <Form.Item name={['lipSync', 'smoothing']} label="平滑度">
                            <Slider min={0} max={1} step={0.1} marks={{ 0: '无', 0.5: '中等', 1: '最大' }} />
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item name={['lipSync', 'intensity']} label="强度">
                            <Slider min={0.1} max={2.0} step={0.1} marks={{ 0.5: '轻微', 1: '正常', 2: '强烈' }} />
                          </Form.Item>
                        </Col>
                      </Row>
                      
                      <Row gutter={16}>
                        <Col span={12}>
                          <Form.Item name={['lipSync', 'enableFormantAnalysis']} label="共振峰分析" valuePropName="checked">
                            <Switch />
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item name={['lipSync', 'enablePitchAnalysis']} label="基频分析" valuePropName="checked">
                            <Switch />
                          </Form.Item>
                        </Col>
                      </Row>
                      
                      <Button
                        type="primary"
                        icon={<SoundOutlined />}
                        onClick={handleTestLipSync}
                        loading={testing && !recording && !previewPlaying}
                      >
                        测试嘴形同步
                      </Button>
                    </Card>
                  ),
                },
              ]}
            />
          </Form>
        </Col>
        
        <Col span={8}>
          <Card title="测试结果" size="small">
            {testResults.length === 0 ? (
              <Text type="secondary">暂无测试结果</Text>
            ) : (
              <List
                size="small"
                dataSource={testResults}
                renderItem={(result, index) => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={
                        <Avatar
                          icon={
                            result.type === 'recognition' ? <MicrophoneOutlined /> :
                            result.type === 'synthesis' ? <AudioOutlined /> :
                            <SoundOutlined />
                          }
                          style={{
                            backgroundColor: result.success ? '#52c41a' : '#ff4d4f'
                          }}
                        />
                      }
                      title={
                        <Space>
                          <span>
                            {result.type === 'recognition' ? '语音识别' :
                             result.type === 'synthesis' ? '语音合成' : '嘴形同步'}
                          </span>
                          <Tag color={result.success ? 'green' : 'red'}>
                            {result.success ? '成功' : '失败'}
                          </Tag>
                        </Space>
                      }
                      description={
                        <div>
                          <div>耗时: {result.duration}ms</div>
                          <div>质量: {(result.quality * 100).toFixed(0)}%</div>
                          <Progress
                            percent={result.quality * 100}
                            size="small"
                            status={result.quality > 0.8 ? 'success' : result.quality > 0.6 ? 'normal' : 'exception'}
                          />
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            )}
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default VoiceConfigPanel;
