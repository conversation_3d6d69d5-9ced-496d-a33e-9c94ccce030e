/**
 * 知识库管理面板
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Table,
  Space,
  Modal,
  Form,
  Input,
  Upload,
  Progress,
  Tag,
  Tooltip,
  Dropdown,
  message,
  Popconfirm,
  Typography,
  Divider,
  Statistic,
  Row,
  Col,
} from 'antd';
import {
  PlusOutlined,
  UploadOutlined,
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  MoreOutlined,
  FileTextOutlined,
  DatabaseOutlined,
  SearchOutlined,
  DownloadOutlined,
  SyncOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import type { ColumnsType } from 'antd/es/table';
import type { UploadFile } from 'antd/es/upload/interface';

const { Title, Text } = Typography;
const { TextArea } = Input;

/**
 * 知识库接口
 */
interface KnowledgeBase {
  id: string;
  name: string;
  description: string;
  sceneId?: string;
  documentCount: number;
  vectorCount: number;
  status: 'active' | 'inactive' | 'processing';
  createdAt: string;
  updatedAt: string;
  size: number; // 字节
}

/**
 * 文档接口
 */
interface Document {
  id: string;
  filename: string;
  fileSize: number;
  mimeType: string;
  status: 'processing' | 'completed' | 'failed';
  chunkCount?: number;
  vectorCount?: number;
  uploadedAt: string;
  processedAt?: string;
  errorMessage?: string;
}

/**
 * 知识库管理面板组件
 */
const KnowledgeBasePanel: React.FC = () => {
  const { t } = useTranslation();
  const [knowledgeBases, setKnowledgeBases] = useState<KnowledgeBase[]>([]);
  const [selectedKnowledgeBase, setSelectedKnowledgeBase] = useState<KnowledgeBase | null>(null);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState(false);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [form] = Form.useForm();

  /**
   * 加载知识库列表
   */
  const loadKnowledgeBases = async () => {
    setLoading(true);
    try {
      // 这里调用API获取知识库列表
      const mockData: KnowledgeBase[] = [
        {
          id: '1',
          name: '医疗知识库',
          description: '包含医疗设备和健康知识的综合知识库',
          sceneId: 'scene_1',
          documentCount: 25,
          vectorCount: 1250,
          status: 'active',
          createdAt: '2024-01-15T10:30:00Z',
          updatedAt: '2024-01-20T15:45:00Z',
          size: 52428800, // 50MB
        },
        {
          id: '2',
          name: '教育培训知识库',
          description: '企业培训和教育相关的文档资料',
          documentCount: 18,
          vectorCount: 890,
          status: 'active',
          createdAt: '2024-01-10T09:15:00Z',
          updatedAt: '2024-01-18T14:20:00Z',
          size: 31457280, // 30MB
        },
      ];
      setKnowledgeBases(mockData);
    } catch (error) {
      message.error('加载知识库列表失败');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 加载文档列表
   */
  const loadDocuments = async (knowledgeBaseId: string) => {
    try {
      // 这里调用API获取文档列表
      const mockData: Document[] = [
        {
          id: '1',
          filename: '医疗设备操作手册.pdf',
          fileSize: 2048000,
          mimeType: 'application/pdf',
          status: 'completed',
          chunkCount: 45,
          vectorCount: 45,
          uploadedAt: '2024-01-15T10:30:00Z',
          processedAt: '2024-01-15T10:35:00Z',
        },
        {
          id: '2',
          filename: '健康知识问答.docx',
          fileSize: 1024000,
          mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          status: 'processing',
          uploadedAt: '2024-01-20T15:45:00Z',
        },
      ];
      setDocuments(mockData);
    } catch (error) {
      message.error('加载文档列表失败');
    }
  };

  /**
   * 创建知识库
   */
  const handleCreateKnowledgeBase = async (values: any) => {
    try {
      // 这里调用API创建知识库
      console.log('创建知识库:', values);
      message.success('知识库创建成功');
      setCreateModalVisible(false);
      form.resetFields();
      loadKnowledgeBases();
    } catch (error) {
      message.error('创建知识库失败');
    }
  };

  /**
   * 删除知识库
   */
  const handleDeleteKnowledgeBase = async (id: string) => {
    try {
      // 这里调用API删除知识库
      console.log('删除知识库:', id);
      message.success('知识库删除成功');
      loadKnowledgeBases();
    } catch (error) {
      message.error('删除知识库失败');
    }
  };

  /**
   * 上传文档
   */
  const handleUploadDocuments = async () => {
    if (!selectedKnowledgeBase || fileList.length === 0) {
      message.warning('请选择知识库和文件');
      return;
    }

    try {
      // 这里调用API上传文档
      console.log('上传文档到知识库:', selectedKnowledgeBase.id, fileList);
      message.success('文档上传成功');
      setUploadModalVisible(false);
      setFileList([]);
      loadDocuments(selectedKnowledgeBase.id);
    } catch (error) {
      message.error('文档上传失败');
    }
  };

  /**
   * 格式化文件大小
   */
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  /**
   * 知识库表格列定义
   */
  const knowledgeBaseColumns: ColumnsType<KnowledgeBase> = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Space>
          <DatabaseOutlined />
          <span>{text}</span>
          <Tag color={record.status === 'active' ? 'green' : 'orange'}>
            {record.status === 'active' ? '活跃' : '非活跃'}
          </Tag>
        </Space>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '文档数',
      dataIndex: 'documentCount',
      key: 'documentCount',
      width: 80,
    },
    {
      title: '向量数',
      dataIndex: 'vectorCount',
      key: 'vectorCount',
      width: 80,
    },
    {
      title: '大小',
      dataIndex: 'size',
      key: 'size',
      width: 80,
      render: (size) => formatFileSize(size),
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => {
                setSelectedKnowledgeBase(record);
                loadDocuments(record.id);
              }}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button type="text" icon={<EditOutlined />} />
          </Tooltip>
          <Popconfirm
            title="确定删除这个知识库吗？"
            onConfirm={() => handleDeleteKnowledgeBase(record.id)}
          >
            <Button type="text" danger icon={<DeleteOutlined />} />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  /**
   * 文档表格列定义
   */
  const documentColumns: ColumnsType<Document> = [
    {
      title: '文件名',
      dataIndex: 'filename',
      key: 'filename',
      render: (text, record) => (
        <Space>
          <FileTextOutlined />
          <span>{text}</span>
        </Space>
      ),
    },
    {
      title: '大小',
      dataIndex: 'fileSize',
      key: 'fileSize',
      width: 80,
      render: (size) => formatFileSize(size),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => {
        const statusMap = {
          processing: { color: 'blue', text: '处理中' },
          completed: { color: 'green', text: '已完成' },
          failed: { color: 'red', text: '失败' },
        };
        const config = statusMap[status as keyof typeof statusMap];
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '分块数',
      dataIndex: 'chunkCount',
      key: 'chunkCount',
      width: 80,
    },
    {
      title: '上传时间',
      dataIndex: 'uploadedAt',
      key: 'uploadedAt',
      width: 120,
      render: (date) => new Date(date).toLocaleDateString(),
    },
    {
      title: '操作',
      key: 'actions',
      width: 80,
      render: (_, record) => (
        <Dropdown
          menu={{
            items: [
              {
                key: 'download',
                icon: <DownloadOutlined />,
                label: '下载',
              },
              {
                key: 'reprocess',
                icon: <SyncOutlined />,
                label: '重新处理',
                disabled: record.status === 'processing',
              },
              {
                key: 'delete',
                icon: <DeleteOutlined />,
                label: '删除',
                danger: true,
              },
            ],
          }}
        >
          <Button type="text" icon={<MoreOutlined />} />
        </Dropdown>
      ),
    },
  ];

  useEffect(() => {
    loadKnowledgeBases();
  }, []);

  return (
    <div style={{ padding: '16px', height: '100%', overflow: 'auto' }}>
      <div style={{ marginBottom: '16px' }}>
        <Title level={4}>知识库管理</Title>
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setCreateModalVisible(true)}
          >
            创建知识库
          </Button>
          <Button
            icon={<UploadOutlined />}
            onClick={() => setUploadModalVisible(true)}
            disabled={!selectedKnowledgeBase}
          >
            上传文档
          </Button>
        </Space>
      </div>

      {/* 统计信息 */}
      <Row gutter={16} style={{ marginBottom: '16px' }}>
        <Col span={6}>
          <Card size="small">
            <Statistic
              title="知识库总数"
              value={knowledgeBases.length}
              prefix={<DatabaseOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <Statistic
              title="文档总数"
              value={knowledgeBases.reduce((sum, kb) => sum + kb.documentCount, 0)}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <Statistic
              title="向量总数"
              value={knowledgeBases.reduce((sum, kb) => sum + kb.vectorCount, 0)}
              prefix={<SearchOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <Statistic
              title="总大小"
              value={formatFileSize(knowledgeBases.reduce((sum, kb) => sum + kb.size, 0))}
            />
          </Card>
        </Col>
      </Row>

      {/* 知识库列表 */}
      <Card title="知识库列表" style={{ marginBottom: '16px' }}>
        <Table
          columns={knowledgeBaseColumns}
          dataSource={knowledgeBases}
          rowKey="id"
          loading={loading}
          size="small"
          pagination={{ pageSize: 10 }}
          onRow={(record) => ({
            onClick: () => {
              setSelectedKnowledgeBase(record);
              loadDocuments(record.id);
            },
          })}
          rowSelection={{
            type: 'radio',
            selectedRowKeys: selectedKnowledgeBase ? [selectedKnowledgeBase.id] : [],
            onSelect: (record) => {
              setSelectedKnowledgeBase(record);
              loadDocuments(record.id);
            },
          }}
        />
      </Card>

      {/* 文档列表 */}
      {selectedKnowledgeBase && (
        <Card title={`文档列表 - ${selectedKnowledgeBase.name}`}>
          <Table
            columns={documentColumns}
            dataSource={documents}
            rowKey="id"
            size="small"
            pagination={{ pageSize: 10 }}
          />
        </Card>
      )}

      {/* 创建知识库模态框 */}
      <Modal
        title="创建知识库"
        open={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        onOk={() => form.submit()}
      >
        <Form form={form} layout="vertical" onFinish={handleCreateKnowledgeBase}>
          <Form.Item
            name="name"
            label="知识库名称"
            rules={[{ required: true, message: '请输入知识库名称' }]}
          >
            <Input placeholder="请输入知识库名称" />
          </Form.Item>
          <Form.Item
            name="description"
            label="描述"
            rules={[{ required: true, message: '请输入描述' }]}
          >
            <TextArea rows={3} placeholder="请输入知识库描述" />
          </Form.Item>
          <Form.Item name="sceneId" label="关联场景">
            <Input placeholder="可选：关联的场景ID" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 上传文档模态框 */}
      <Modal
        title="上传文档"
        open={uploadModalVisible}
        onCancel={() => setUploadModalVisible(false)}
        onOk={handleUploadDocuments}
        width={600}
      >
        <div style={{ marginBottom: '16px' }}>
          <Text strong>目标知识库：</Text>
          <Text>{selectedKnowledgeBase?.name}</Text>
        </div>
        <Upload.Dragger
          multiple
          fileList={fileList}
          onChange={({ fileList }) => setFileList(fileList)}
          beforeUpload={() => false} // 阻止自动上传
          accept=".pdf,.doc,.docx,.txt,.md,.ppt,.pptx,.xls,.xlsx"
        >
          <p className="ant-upload-drag-icon">
            <UploadOutlined />
          </p>
          <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p className="ant-upload-hint">
            支持 PDF、Word、PowerPoint、Excel、文本等格式
          </p>
        </Upload.Dragger>
      </Modal>
    </div>
  );
};

export default KnowledgeBasePanel;
