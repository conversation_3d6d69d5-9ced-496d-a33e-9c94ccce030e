/**
 * RAG应用快速启动组件
 */
import React, { useState } from 'react';
import {
  Card,
  Button,
  Steps,
  Space,
  Typography,
  Row,
  Col,
  Statistic,
  Progress,
  Tag,
  Modal,
  message,
  Tooltip,
} from 'antd';
import {
  RobotOutlined,
  DatabaseOutlined,
  MessageOutlined,
  PlayCircleOutlined,
  SettingOutlined,
  PlusOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Title, Text, Paragraph } = Typography;
const { Step } = Steps;

/**
 * RAG应用状态
 */
interface RAGAppStatus {
  knowledgeBase: {
    count: number;
    ready: number;
  };
  avatars: {
    count: number;
    configured: number;
  };
  applications: {
    count: number;
    active: number;
  };
  voice: {
    configured: boolean;
    tested: boolean;
  };
}

/**
 * RAG应用快速启动组件
 */
const RAGQuickStart: React.FC = () => {
  const { t } = useTranslation();
  const [currentStep, setCurrentStep] = useState(0);
  const [modalVisible, setModalVisible] = useState(false);
  const [status, setStatus] = useState<RAGAppStatus>({
    knowledgeBase: { count: 2, ready: 1 },
    avatars: { count: 3, configured: 2 },
    applications: { count: 2, active: 1 },
    voice: { configured: true, tested: false },
  });

  /**
   * 快速启动步骤
   */
  const quickStartSteps = [
    {
      title: '创建知识库',
      description: '上传文档并构建知识库',
      icon: <DatabaseOutlined />,
      action: () => {
        // 打开知识库管理面板
        message.info('正在打开知识库管理面板...');
      },
      status: status.knowledgeBase.ready > 0 ? 'finish' : 'wait',
    },
    {
      title: '配置数字人',
      description: '设置数字人外观和行为',
      icon: <RobotOutlined />,
      action: () => {
        // 打开数字人配置面板
        message.info('正在打开数字人配置面板...');
      },
      status: status.avatars.configured > 0 ? 'finish' : 'wait',
    },
    {
      title: '配置语音',
      description: '设置语音识别和合成',
      icon: <MessageOutlined />,
      action: () => {
        // 打开语音配置面板
        message.info('正在打开语音配置面板...');
      },
      status: status.voice.configured ? 'finish' : 'wait',
    },
    {
      title: '创建应用',
      description: '组合组件创建RAG应用',
      icon: <PlayCircleOutlined />,
      action: () => {
        // 打开RAG应用管理面板
        message.info('正在打开RAG应用管理面板...');
      },
      status: status.applications.count > 0 ? 'finish' : 'wait',
    },
  ];

  /**
   * 获取整体完成度
   */
  const getOverallProgress = (): number => {
    const completedSteps = quickStartSteps.filter(step => step.status === 'finish').length;
    return (completedSteps / quickStartSteps.length) * 100;
  };

  /**
   * 获取状态颜色
   */
  const getStatusColor = (value: number, total: number): string => {
    const ratio = value / total;
    if (ratio >= 1) return '#52c41a';
    if (ratio >= 0.5) return '#faad14';
    return '#ff4d4f';
  };

  /**
   * 快速创建应用
   */
  const handleQuickCreate = () => {
    setModalVisible(true);
  };

  /**
   * 执行快速创建
   */
  const executeQuickCreate = async () => {
    try {
      // 这里执行快速创建逻辑
      message.loading('正在创建RAG应用...', 2);
      
      setTimeout(() => {
        message.success('RAG应用创建成功！');
        setModalVisible(false);
        // 更新状态
        setStatus(prev => ({
          ...prev,
          applications: {
            count: prev.applications.count + 1,
            active: prev.applications.active + 1,
          },
        }));
      }, 2000);
      
    } catch (error) {
      message.error('创建RAG应用失败');
    }
  };

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={3}>
          <RobotOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
          RAG应用快速启动
        </Title>
        <Paragraph type="secondary">
          快速创建和配置您的RAG（检索增强生成）应用，让数字人具备智能问答能力。
        </Paragraph>
      </div>

      {/* 整体进度 */}
      <Card style={{ marginBottom: '24px' }}>
        <Row gutter={24} align="middle">
          <Col span={6}>
            <div style={{ textAlign: 'center' }}>
              <Progress
                type="circle"
                percent={getOverallProgress()}
                size={80}
                strokeColor={{
                  '0%': '#108ee9',
                  '100%': '#87d068',
                }}
              />
              <div style={{ marginTop: '8px' }}>
                <Text strong>整体进度</Text>
              </div>
            </div>
          </Col>
          <Col span={18}>
            <Row gutter={16}>
              <Col span={6}>
                <Statistic
                  title="知识库"
                  value={status.knowledgeBase.ready}
                  suffix={`/ ${status.knowledgeBase.count}`}
                  valueStyle={{ color: getStatusColor(status.knowledgeBase.ready, status.knowledgeBase.count) }}
                  prefix={<DatabaseOutlined />}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="数字人"
                  value={status.avatars.configured}
                  suffix={`/ ${status.avatars.count}`}
                  valueStyle={{ color: getStatusColor(status.avatars.configured, status.avatars.count) }}
                  prefix={<RobotOutlined />}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="活跃应用"
                  value={status.applications.active}
                  suffix={`/ ${status.applications.count}`}
                  valueStyle={{ color: getStatusColor(status.applications.active, status.applications.count) }}
                  prefix={<PlayCircleOutlined />}
                />
              </Col>
              <Col span={6}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: status.voice.configured ? '#52c41a' : '#ff4d4f' }}>
                    {status.voice.configured ? <CheckCircleOutlined /> : <ExclamationCircleOutlined />}
                  </div>
                  <div style={{ fontSize: '14px', color: '#666' }}>语音配置</div>
                  <div style={{ fontSize: '12px', color: '#999' }}>
                    {status.voice.configured ? '已配置' : '未配置'}
                  </div>
                </div>
              </Col>
            </Row>
          </Col>
        </Row>
      </Card>

      {/* 快速启动步骤 */}
      <Card title="快速启动向导" style={{ marginBottom: '24px' }}>
        <Steps current={currentStep} direction="vertical">
          {quickStartSteps.map((step, index) => (
            <Step
              key={index}
              title={step.title}
              description={step.description}
              icon={step.icon}
              status={step.status}
              subTitle={
                <Space>
                  <Button
                    type="link"
                    size="small"
                    onClick={step.action}
                  >
                    {step.status === 'finish' ? '查看' : '开始'}
                  </Button>
                  {step.status === 'finish' && (
                    <Tag color="green" size="small">已完成</Tag>
                  )}
                </Space>
              }
            />
          ))}
        </Steps>
      </Card>

      {/* 快速操作 */}
      <Card title="快速操作">
        <Row gutter={16}>
          <Col span={8}>
            <Card size="small" hoverable>
              <div style={{ textAlign: 'center' }}>
                <DatabaseOutlined style={{ fontSize: '32px', color: '#1890ff', marginBottom: '8px' }} />
                <div style={{ marginBottom: '8px' }}>
                  <Text strong>知识库管理</Text>
                </div>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  上传文档，构建知识库
                </Text>
                <div style={{ marginTop: '12px' }}>
                  <Button type="primary" size="small" block>
                    管理知识库
                  </Button>
                </div>
              </div>
            </Card>
          </Col>
          <Col span={8}>
            <Card size="small" hoverable>
              <div style={{ textAlign: 'center' }}>
                <RobotOutlined style={{ fontSize: '32px', color: '#52c41a', marginBottom: '8px' }} />
                <div style={{ marginBottom: '8px' }}>
                  <Text strong>数字人配置</Text>
                </div>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  设置外观、语音、动画
                </Text>
                <div style={{ marginTop: '12px' }}>
                  <Button type="primary" size="small" block>
                    配置数字人
                  </Button>
                </div>
              </div>
            </Card>
          </Col>
          <Col span={8}>
            <Card size="small" hoverable>
              <div style={{ textAlign: 'center' }}>
                <MessageOutlined style={{ fontSize: '32px', color: '#faad14', marginBottom: '8px' }} />
                <div style={{ marginBottom: '8px' }}>
                  <Text strong>对话测试</Text>
                </div>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  测试RAG对话效果
                </Text>
                <div style={{ marginTop: '12px' }}>
                  <Button type="primary" size="small" block>
                    开始测试
                  </Button>
                </div>
              </div>
            </Card>
          </Col>
        </Row>
        
        <div style={{ marginTop: '16px', textAlign: 'center' }}>
          <Space>
            <Button
              type="primary"
              size="large"
              icon={<PlusOutlined />}
              onClick={handleQuickCreate}
              disabled={getOverallProgress() < 75}
            >
              一键创建RAG应用
            </Button>
            <Tooltip title="需要完成至少75%的配置才能快速创建">
              <Button icon={<SettingOutlined />}>
                高级配置
              </Button>
            </Tooltip>
          </Space>
        </div>
      </Card>

      {/* 快速创建确认模态框 */}
      <Modal
        title="快速创建RAG应用"
        open={modalVisible}
        onOk={executeQuickCreate}
        onCancel={() => setModalVisible(false)}
        okText="创建"
        cancelText="取消"
      >
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <RobotOutlined style={{ fontSize: '48px', color: '#1890ff', marginBottom: '16px' }} />
          <Title level={4}>确认创建RAG应用？</Title>
          <Paragraph type="secondary">
            系统将使用当前配置自动创建一个RAG应用，包括：
          </Paragraph>
          <ul style={{ textAlign: 'left', display: 'inline-block' }}>
            <li>关联已配置的知识库</li>
            <li>使用已配置的数字人</li>
            <li>应用当前语音设置</li>
            <li>创建默认场景关联</li>
          </ul>
        </div>
      </Modal>
    </div>
  );
};

export default RAGQuickStart;
