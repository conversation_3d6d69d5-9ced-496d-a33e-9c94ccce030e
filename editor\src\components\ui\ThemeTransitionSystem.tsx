/**
 * ThemeTransitionSystem.tsx
 * 
 * 主题切换动画系统，提供流畅的主题过渡效果
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { gsap } from 'gsap';

/**
 * 主题过渡配置
 */
export interface ThemeTransitionConfig {
  /** 过渡持续时间（毫秒） */
  duration: number;
  /** 过渡缓动函数 */
  easing: string;
  /** 过渡类型 */
  type: 'fade' | 'slide' | 'ripple' | 'morph' | 'particles' | 'wave';
  /** 是否启用预加载 */
  preload: boolean;
  /** 是否启用硬件加速 */
  useGPU: boolean;
  /** 过渡方向（仅适用于slide类型） */
  direction?: 'left' | 'right' | 'up' | 'down';
  /** 波纹起始位置（仅适用于ripple类型） */
  rippleOrigin?: { x: number; y: number };
  /** 粒子数量（仅适用于particles类型） */
  particleCount?: number;
}

/**
 * 主题数据接口
 */
export interface ThemeData {
  id: string;
  name: string;
  colors: Record<string, string>;
  isDark: boolean;
}

/**
 * 默认配置
 */
const DEFAULT_CONFIG: ThemeTransitionConfig = {
  duration: 800,
  easing: 'power2.inOut',
  type: 'fade',
  preload: true,
  useGPU: true,
  particleCount: 50
};

/**
 * 主题过渡系统Hook
 */
export const useThemeTransition = (
  config: Partial<ThemeTransitionConfig> = {}
) => {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [currentTheme, setCurrentTheme] = useState<ThemeData | null>(null);
  const [nextTheme, setNextTheme] = useState<ThemeData | null>(null);
  
  const transitionRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const timelineRef = useRef<gsap.core.Timeline>();

  // 创建过渡遮罩
  const createTransitionOverlay = useCallback(() => {
    if (!transitionRef.current) return null;

    const overlay = document.createElement('div');
    overlay.className = 'theme-transition-overlay';
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      z-index: 9999;
      pointer-events: none;
      will-change: transform, opacity;
    `;

    document.body.appendChild(overlay);
    return overlay;
  }, []);

  // 淡入淡出过渡
  const fadeTransition = useCallback(async (fromTheme: ThemeData, toTheme: ThemeData) => {
    const overlay = createTransitionOverlay();
    if (!overlay) return;

    // 设置初始状态
    overlay.style.background = fromTheme.colors.background;
    overlay.style.opacity = '0';

    const tl = gsap.timeline({
      onComplete: () => {
        document.body.removeChild(overlay);
        setIsTransitioning(false);
      }
    });

    // 淡入
    tl.to(overlay, {
      opacity: 1,
      duration: finalConfig.duration / 2000,
      ease: finalConfig.easing
    })
    // 切换主题颜色
    .call(() => {
      overlay.style.background = toTheme.colors.background;
      applyThemeColors(toTheme);
    })
    // 淡出
    .to(overlay, {
      opacity: 0,
      duration: finalConfig.duration / 2000,
      ease: finalConfig.easing
    });

    timelineRef.current = tl;
  }, [finalConfig, createTransitionOverlay]);

  // 滑动过渡
  const slideTransition = useCallback(async (fromTheme: ThemeData, toTheme: ThemeData) => {
    const overlay = createTransitionOverlay();
    if (!overlay) return;

    const direction = finalConfig.direction || 'right';
    const isHorizontal = direction === 'left' || direction === 'right';
    const isPositive = direction === 'right' || direction === 'down';

    // 创建两个面板
    const fromPanel = document.createElement('div');
    const toPanel = document.createElement('div');

    [fromPanel, toPanel].forEach(panel => {
      panel.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      `;
    });

    fromPanel.style.background = fromTheme.colors.background;
    toPanel.style.background = toTheme.colors.background;

    // 设置初始位置
    if (isHorizontal) {
      toPanel.style.transform = `translateX(${isPositive ? '100%' : '-100%'})`;
    } else {
      toPanel.style.transform = `translateY(${isPositive ? '100%' : '-100%'})`;
    }

    overlay.appendChild(fromPanel);
    overlay.appendChild(toPanel);

    const tl = gsap.timeline({
      onComplete: () => {
        document.body.removeChild(overlay);
        setIsTransitioning(false);
      }
    });

    // 同时移动两个面板
    if (isHorizontal) {
      tl.to(fromPanel, {
        x: isPositive ? '-100%' : '100%',
        duration: finalConfig.duration / 1000,
        ease: finalConfig.easing
      })
      .to(toPanel, {
        x: '0%',
        duration: finalConfig.duration / 1000,
        ease: finalConfig.easing
      }, 0)
      .call(() => applyThemeColors(toTheme), [], finalConfig.duration / 2000);
    } else {
      tl.to(fromPanel, {
        y: isPositive ? '-100%' : '100%',
        duration: finalConfig.duration / 1000,
        ease: finalConfig.easing
      })
      .to(toPanel, {
        y: '0%',
        duration: finalConfig.duration / 1000,
        ease: finalConfig.easing
      }, 0)
      .call(() => applyThemeColors(toTheme), [], finalConfig.duration / 2000);
    }

    timelineRef.current = tl;
  }, [finalConfig, createTransitionOverlay]);

  // 波纹过渡
  const rippleTransition = useCallback(async (fromTheme: ThemeData, toTheme: ThemeData) => {
    const overlay = createTransitionOverlay();
    if (!overlay) return;

    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;
    canvas.style.cssText = `
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    `;

    overlay.appendChild(canvas);

    const centerX = finalConfig.rippleOrigin?.x || canvas.width / 2;
    const centerY = finalConfig.rippleOrigin?.y || canvas.height / 2;
    const maxRadius = Math.sqrt(canvas.width ** 2 + canvas.height ** 2);

    let currentRadius = 0;

    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // 绘制背景
      ctx.fillStyle = fromTheme.colors.background;
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // 绘制波纹
      if (currentRadius < maxRadius) {
        ctx.save();
        ctx.beginPath();
        ctx.arc(centerX, centerY, currentRadius, 0, Math.PI * 2);
        ctx.clip();
        
        ctx.fillStyle = toTheme.colors.background;
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        ctx.restore();
      }
    };

    const tl = gsap.timeline({
      onUpdate: animate,
      onComplete: () => {
        applyThemeColors(toTheme);
        document.body.removeChild(overlay);
        setIsTransitioning(false);
      }
    });

    tl.to({ radius: currentRadius }, {
      radius: maxRadius,
      duration: finalConfig.duration / 1000,
      ease: finalConfig.easing,
      onUpdate: function() {
        currentRadius = this.targets()[0].radius;
      }
    });

    timelineRef.current = tl;
  }, [finalConfig, createTransitionOverlay]);

  // 粒子过渡
  const particleTransition = useCallback(async (fromTheme: ThemeData, toTheme: ThemeData) => {
    const overlay = createTransitionOverlay();
    if (!overlay) return;

    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;
    canvas.style.cssText = `
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    `;

    overlay.appendChild(canvas);

    // 创建粒子
    const particles: Array<{
      x: number;
      y: number;
      vx: number;
      vy: number;
      size: number;
      opacity: number;
      color: string;
    }> = [];

    for (let i = 0; i < (finalConfig.particleCount || 50); i++) {
      particles.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        vx: (Math.random() - 0.5) * 4,
        vy: (Math.random() - 0.5) * 4,
        size: Math.random() * 6 + 2,
        opacity: 0,
        color: toTheme.colors.primary
      });
    }

    let progress = 0;

    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // 绘制背景渐变
      const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
      gradient.addColorStop(0, fromTheme.colors.background);
      gradient.addColorStop(progress, toTheme.colors.background);
      gradient.addColorStop(1, fromTheme.colors.background);
      
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // 绘制和更新粒子
      particles.forEach(particle => {
        particle.x += particle.vx;
        particle.y += particle.vy;
        particle.opacity = Math.sin(progress * Math.PI) * 0.8;

        // 边界检测
        if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
        if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;

        // 绘制粒子
        ctx.save();
        ctx.globalAlpha = particle.opacity;
        ctx.fillStyle = particle.color;
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();
      });
    };

    const tl = gsap.timeline({
      onUpdate: animate,
      onComplete: () => {
        applyThemeColors(toTheme);
        document.body.removeChild(overlay);
        setIsTransitioning(false);
      }
    });

    tl.to({ progress: 0 }, {
      progress: 1,
      duration: finalConfig.duration / 1000,
      ease: finalConfig.easing,
      onUpdate: function() {
        progress = this.targets()[0].progress;
      }
    });

    timelineRef.current = tl;
  }, [finalConfig, createTransitionOverlay]);

  // 应用主题颜色
  const applyThemeColors = useCallback((theme: ThemeData) => {
    const root = document.documentElement;
    Object.entries(theme.colors).forEach(([key, value]) => {
      root.style.setProperty(`--theme-${key}`, value);
    });
    
    // 更新body类名
    document.body.className = document.body.className.replace(/theme-\w+/g, '');
    document.body.classList.add(`theme-${theme.id}`);
    
    setCurrentTheme(theme);
  }, []);

  // 执行主题过渡
  const transitionToTheme = useCallback(async (newTheme: ThemeData) => {
    if (isTransitioning || !currentTheme) {
      setCurrentTheme(newTheme);
      applyThemeColors(newTheme);
      return;
    }

    setIsTransitioning(true);
    setNextTheme(newTheme);

    // 停止当前动画
    if (timelineRef.current) {
      timelineRef.current.kill();
    }

    try {
      switch (finalConfig.type) {
        case 'slide':
          await slideTransition(currentTheme, newTheme);
          break;
        case 'ripple':
          await rippleTransition(currentTheme, newTheme);
          break;
        case 'particles':
          await particleTransition(currentTheme, newTheme);
          break;
        case 'fade':
        default:
          await fadeTransition(currentTheme, newTheme);
          break;
      }
    } catch (error) {
      console.error('主题过渡失败:', error);
      applyThemeColors(newTheme);
      setIsTransitioning(false);
    }
  }, [currentTheme, isTransitioning, finalConfig, fadeTransition, slideTransition, rippleTransition, particleTransition, applyThemeColors]);

  // 取消过渡
  const cancelTransition = useCallback(() => {
    if (timelineRef.current) {
      timelineRef.current.kill();
    }
    
    // 清理DOM
    const overlays = document.querySelectorAll('.theme-transition-overlay');
    overlays.forEach(overlay => overlay.remove());
    
    setIsTransitioning(false);
    setNextTheme(null);
  }, []);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      cancelTransition();
    };
  }, [cancelTransition]);

  return {
    currentTheme,
    nextTheme,
    isTransitioning,
    transitionToTheme,
    cancelTransition,
    setCurrentTheme: (theme: ThemeData) => {
      setCurrentTheme(theme);
      applyThemeColors(theme);
    }
  };
};

/**
 * 主题过渡组件
 */
export interface ThemeTransitionProps {
  config?: Partial<ThemeTransitionConfig>;
  children: React.ReactNode;
  onTransitionStart?: (fromTheme: ThemeData, toTheme: ThemeData) => void;
  onTransitionComplete?: (theme: ThemeData) => void;
}

export const ThemeTransition: React.FC<ThemeTransitionProps> = ({
  config,
  children,
  onTransitionStart,
  onTransitionComplete
}) => {
  const {
    currentTheme,
    isTransitioning,
    transitionToTheme,
    setCurrentTheme
  } = useThemeTransition(config);

  const transitionRef = useRef<HTMLDivElement>(null);

  // 监听主题变化事件
  useEffect(() => {
    const handleThemeChange = (event: CustomEvent<ThemeData>) => {
      const newTheme = event.detail;
      
      if (currentTheme) {
        onTransitionStart?.(currentTheme, newTheme);
      }
      
      transitionToTheme(newTheme).then(() => {
        onTransitionComplete?.(newTheme);
      });
    };

    window.addEventListener('themeChange', handleThemeChange as EventListener);
    
    return () => {
      window.removeEventListener('themeChange', handleThemeChange as EventListener);
    };
  }, [currentTheme, transitionToTheme, onTransitionStart, onTransitionComplete]);

  return (
    <div ref={transitionRef} className={`theme-transition-container ${isTransitioning ? 'transitioning' : ''}`}>
      {children}
    </div>
  );
};

// 工具函数：触发主题切换
export const triggerThemeChange = (theme: ThemeData) => {
  const event = new CustomEvent('themeChange', { detail: theme });
  window.dispatchEvent(event);
};

export default ThemeTransition;
