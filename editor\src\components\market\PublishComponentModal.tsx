/**
 * PublishComponentModal.tsx
 * 
 * 发布组件模态框
 */

import React, { useState, useCallback } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  Upload,
  Button,
  Space,
  Steps,
  Card,
  Tag,
  message,
  Row,
  Col,
  Divider,
  Alert
} from 'antd';
import {
  UploadOutlined,
  PlusOutlined,
  DeleteOutlined,
  EyeOutlined,
  CloudUploadOutlined
} from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd/es/upload';
import { ComponentMarketService } from '../../services/ComponentMarketService';
import './PublishComponentModal.module.css';

const { TextArea } = Input;
const { Step } = Steps;

/**
 * 发布组件表单数据
 */
interface PublishFormData {
  name: string;
  description: string;
  category: string;
  tags: string[];
  version: string;
  license: string;
  documentation?: string;
  dependencies?: string[];
}

/**
 * 发布组件模态框属性
 */
export interface PublishComponentModalProps {
  /** 是否显示 */
  visible: boolean;
  /** 关闭回调 */
  onClose: () => void;
  /** 成功回调 */
  onSuccess?: () => void;
  /** 初始组件数据 */
  initialData?: any;
}

/**
 * 预定义分类
 */
const CATEGORIES = [
  { label: '布局组件', value: 'layout' },
  { label: '表单组件', value: 'form' },
  { label: '数据展示', value: 'display' },
  { label: '导航组件', value: 'navigation' },
  { label: '反馈组件', value: 'feedback' },
  { label: '通用组件', value: 'general' },
  { label: '业务组件', value: 'business' },
  { label: '图表组件', value: 'chart' },
  { label: '媒体组件', value: 'media' },
  { label: '其他', value: 'other' }
];

/**
 * 许可证选项
 */
const LICENSES = [
  { label: 'MIT', value: 'MIT' },
  { label: 'Apache 2.0', value: 'Apache-2.0' },
  { label: 'GPL v3', value: 'GPL-3.0' },
  { label: 'BSD 3-Clause', value: 'BSD-3-Clause' },
  { label: 'ISC', value: 'ISC' },
  { label: '自定义', value: 'Custom' }
];

/**
 * 发布组件模态框
 */
export const PublishComponentModal: React.FC<PublishComponentModalProps> = ({
  visible,
  onClose,
  onSuccess,
  initialData
}) => {
  const [form] = Form.useForm();
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [componentFile, setComponentFile] = useState<UploadFile[]>([]);
  const [previewImage, setPreviewImage] = useState<UploadFile[]>([]);
  const [customTags, setCustomTags] = useState<string[]>([]);
  const [newTag, setNewTag] = useState('');

  const marketService = new ComponentMarketService();

  // 重置表单
  const resetForm = useCallback(() => {
    form.resetFields();
    setCurrentStep(0);
    setComponentFile([]);
    setPreviewImage([]);
    setCustomTags([]);
    setNewTag('');
  }, [form]);

  // 处理关闭
  const handleClose = useCallback(() => {
    resetForm();
    onClose();
  }, [resetForm, onClose]);

  // 下一步
  const handleNext = useCallback(async () => {
    try {
      if (currentStep === 0) {
        // 验证基本信息
        await form.validateFields(['name', 'description', 'category', 'version']);
      } else if (currentStep === 1) {
        // 验证标签和许可证
        await form.validateFields(['license']);
      }
      setCurrentStep(prev => prev + 1);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  }, [currentStep, form]);

  // 上一步
  const handlePrev = useCallback(() => {
    setCurrentStep(prev => prev - 1);
  }, []);

  // 添加标签
  const handleAddTag = useCallback(() => {
    if (newTag && !customTags.includes(newTag)) {
      const updatedTags = [...customTags, newTag];
      setCustomTags(updatedTags);
      form.setFieldsValue({ tags: updatedTags });
      setNewTag('');
    }
  }, [newTag, customTags, form]);

  // 删除标签
  const handleRemoveTag = useCallback((tagToRemove: string) => {
    const updatedTags = customTags.filter(tag => tag !== tagToRemove);
    setCustomTags(updatedTags);
    form.setFieldsValue({ tags: updatedTags });
  }, [customTags, form]);

  // 处理文件上传
  const handleFileChange: UploadProps['onChange'] = useCallback((info) => {
    setComponentFile(info.fileList);
  }, []);

  // 处理预览图片上传
  const handleImageChange: UploadProps['onChange'] = useCallback((info) => {
    setPreviewImage(info.fileList);
  }, []);

  // 提交发布
  const handleSubmit = useCallback(async () => {
    try {
      setLoading(true);
      
      const values = await form.validateFields();
      
      // 准备表单数据
      const formData = new FormData();
      
      // 添加基本信息
      Object.keys(values).forEach(key => {
        if (values[key] !== undefined && values[key] !== null) {
          if (Array.isArray(values[key])) {
            formData.append(key, JSON.stringify(values[key]));
          } else {
            formData.append(key, values[key]);
          }
        }
      });

      // 添加组件数据
      if (initialData) {
        formData.append('componentData', JSON.stringify(initialData));
      }

      // 添加文件
      if (componentFile.length > 0 && componentFile[0].originFileObj) {
        formData.append('componentFile', componentFile[0].originFileObj);
      }

      if (previewImage.length > 0 && previewImage[0].originFileObj) {
        formData.append('previewImage', previewImage[0].originFileObj);
      }

      const response = await marketService.publishComponent(formData);
      
      if (response.success) {
        message.success('组件发布成功，等待审核');
        handleClose();
        onSuccess?.();
      } else {
        message.error(response.message || '发布失败');
      }
    } catch (error) {
      message.error('发布失败，请检查表单信息');
    } finally {
      setLoading(false);
    }
  }, [form, initialData, componentFile, previewImage, marketService, handleClose, onSuccess]);

  // 渲染步骤内容
  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <div className="step-content">
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="name"
                  label="组件名称"
                  rules={[
                    { required: true, message: '请输入组件名称' },
                    { min: 2, max: 50, message: '名称长度为2-50个字符' }
                  ]}
                >
                  <Input placeholder="输入组件名称" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="version"
                  label="版本号"
                  rules={[
                    { required: true, message: '请输入版本号' },
                    { pattern: /^\d+\.\d+\.\d+$/, message: '版本号格式：x.y.z' }
                  ]}
                >
                  <Input placeholder="例如：1.0.0" />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="description"
              label="组件描述"
              rules={[
                { required: true, message: '请输入组件描述' },
                { min: 10, max: 500, message: '描述长度为10-500个字符' }
              ]}
            >
              <TextArea
                rows={4}
                placeholder="详细描述组件的功能和用途"
                showCount
                maxLength={500}
              />
            </Form.Item>

            <Form.Item
              name="category"
              label="组件分类"
              rules={[{ required: true, message: '请选择组件分类' }]}
            >
              <Select placeholder="选择组件分类">
                {CATEGORIES.map(category => (
                  <Select.Option key={category.value} value={category.value}>
                    {category.label}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </div>
        );

      case 1:
        return (
          <div className="step-content">
            <Form.Item label="组件标签">
              <div className="tag-input-container">
                <Space.Compact style={{ width: '100%' }}>
                  <Input
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    placeholder="输入标签名称"
                    onPressEnter={handleAddTag}
                  />
                  <Button type="primary" icon={<PlusOutlined />} onClick={handleAddTag}>
                    添加
                  </Button>
                </Space.Compact>
              </div>
              <div className="tags-display" style={{ marginTop: 8 }}>
                {customTags.map(tag => (
                  <Tag
                    key={tag}
                    closable
                    onClose={() => handleRemoveTag(tag)}
                    color="blue"
                  >
                    {tag}
                  </Tag>
                ))}
              </div>
            </Form.Item>

            <Form.Item
              name="license"
              label="许可证"
              rules={[{ required: true, message: '请选择许可证' }]}
              initialValue="MIT"
            >
              <Select placeholder="选择许可证">
                {LICENSES.map(license => (
                  <Select.Option key={license.value} value={license.value}>
                    {license.label}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item name="dependencies" label="依赖项">
              <Select
                mode="tags"
                placeholder="输入依赖的组件或库"
                style={{ width: '100%' }}
              />
            </Form.Item>

            <Form.Item name="documentation" label="使用文档">
              <TextArea
                rows={6}
                placeholder="输入组件的使用文档（支持Markdown格式）"
                showCount
                maxLength={2000}
              />
            </Form.Item>
          </div>
        );

      case 2:
        return (
          <div className="step-content">
            <Form.Item label="组件文件">
              <Upload
                fileList={componentFile}
                onChange={handleFileChange}
                beforeUpload={() => false}
                accept=".zip,.rar"
                maxCount={1}
              >
                <Button icon={<UploadOutlined />}>
                  上传组件包（可选）
                </Button>
              </Upload>
              <div className="upload-tip">
                支持 .zip 或 .rar 格式，如果不上传文件，将使用当前编辑器中的组件数据
              </div>
            </Form.Item>

            <Form.Item label="预览图片">
              <Upload
                fileList={previewImage}
                onChange={handleImageChange}
                beforeUpload={() => false}
                accept="image/*"
                maxCount={1}
                listType="picture-card"
              >
                <div>
                  <PlusOutlined />
                  <div style={{ marginTop: 8 }}>上传图片</div>
                </div>
              </Upload>
              <div className="upload-tip">
                建议尺寸：400x300像素，支持 JPG、PNG 格式
              </div>
            </Form.Item>

            <Alert
              message="发布须知"
              description={
                <ul>
                  <li>组件发布后需要经过审核才能在市场中显示</li>
                  <li>请确保组件内容原创且不侵犯他人版权</li>
                  <li>组件应该具有良好的可用性和文档说明</li>
                  <li>恶意或低质量组件可能被拒绝或下架</li>
                </ul>
              }
              type="info"
              showIcon
            />
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Modal
      title="发布组件"
      open={visible}
      onCancel={handleClose}
      width={800}
      footer={
        <div className="modal-footer">
          <Space>
            <Button onClick={handleClose}>取消</Button>
            {currentStep > 0 && (
              <Button onClick={handlePrev}>上一步</Button>
            )}
            {currentStep < 2 ? (
              <Button type="primary" onClick={handleNext}>
                下一步
              </Button>
            ) : (
              <Button
                type="primary"
                loading={loading}
                onClick={handleSubmit}
                icon={<CloudUploadOutlined />}
              >
                发布组件
              </Button>
            )}
          </Space>
        </div>
      }
      className="publish-component-modal"
    >
      <Steps current={currentStep} className="publish-steps">
        <Step title="基本信息" description="组件名称、描述和分类" />
        <Step title="详细配置" description="标签、许可证和文档" />
        <Step title="文件上传" description="组件包和预览图片" />
      </Steps>

      <Divider />

      <Form
        form={form}
        layout="vertical"
        initialValues={{
          license: 'MIT',
          tags: []
        }}
      >
        {renderStepContent()}
      </Form>
    </Modal>
  );
};

export default PublishComponentModal;
